digraph G {
"base_link" -> "base_footprint"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"odom" -> "base_link"[label=" Broadcaster: default_authority\nAverage rate: 83.685\nBuffer length: 2.844\nMost recent transform: 34.068\nOldest transform: 31.224\n"];
"base_link" -> "camera_link"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"camera_link" -> "camera_link_optical"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"base_link" -> "front_caster"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"base_link" -> "imu_link"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"base_link" -> "lidar_link"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"base_link" -> "uv_light_link"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"base_footprint" -> "drivewhl_r_link"[label=" Broadcaster: default_authority\nAverage rate: 83.683\nBuffer length: 2.856\nMost recent transform: 34.08\nOldest transform: 31.224\n"];
"base_footprint" -> "drivewhl_l_link"[label=" Broadcaster: default_authority\nAverage rate: 83.683\nBuffer length: 2.856\nMost recent transform: 34.08\nOldest transform: 31.224\n"];
"map" -> "odom"[label=" Broadcaster: default_authority\nAverage rate: 26.558\nBuffer length: 2.824\nMost recent transform: 35.068\nOldest transform: 32.244\n"];
edge [style=invis];
 subgraph cluster_legend { style=bold; color=black; label ="view_frames Result";
"Recorded at time: 1750324964.4080567"[ shape=plaintext ] ;
}->"map";
}