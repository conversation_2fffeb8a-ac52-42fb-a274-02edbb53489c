{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-082a4ecf2ab7a90fe179.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "turtle_bot_gazebo", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "turtle_bot_gazebo_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-turtle_bot_gazebo_uninstall-880130d0a072fe640305.json", "name": "turtle_bot_gazebo_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-0f6a3c73e28c34bfeb90.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/turtle/build/turtle_bot_gazebo", "source": "/home/<USER>/turtle/src/turtle_bot_gazebo"}, "version": {"major": 2, "minor": 3}}