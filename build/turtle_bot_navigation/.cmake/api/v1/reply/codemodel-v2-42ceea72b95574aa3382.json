{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-bf2512209c832227834b.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "turtle_bot_navigation", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "turtle_bot_navigation_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-turtle_bot_navigation_uninstall-bd004dc7335b0df52854.json", "name": "turtle_bot_navigation_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-17bee3fe9f15a63c0fc8.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/turtle/build/turtle_bot_navigation", "source": "/home/<USER>/turtle/src/turtle_bot_navigation"}, "version": {"major": 2, "minor": 3}}