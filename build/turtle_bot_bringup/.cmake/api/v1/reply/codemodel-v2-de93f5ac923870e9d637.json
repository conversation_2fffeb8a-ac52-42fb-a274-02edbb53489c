{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-1ea7051db7f528738c12.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "turtle_bot_bringup", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "turtle_bot_bringup_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-turtle_bot_bringup_uninstall-e266ecc9a576b49f3b1c.json", "name": "turtle_bot_bringup_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-b5376d5cf28c7e3c8de9.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/turtle/build/turtle_bot_bringup", "source": "/home/<USER>/turtle/src/turtle_bot_bringup"}, "version": {"major": 2, "minor": 3}}