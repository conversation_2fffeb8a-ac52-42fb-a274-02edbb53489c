{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-f2648f5d5681b62c4988.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "turtle_bot_description", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "turtle_bot_description_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-turtle_bot_description_uninstall-a5324328ee6512942e4a.json", "name": "turtle_bot_description_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-18b0877e32b249c763ab.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/turtle/build/turtle_bot_description", "source": "/home/<USER>/turtle/src/turtle_bot_description"}, "version": {"major": 2, "minor": 3}}