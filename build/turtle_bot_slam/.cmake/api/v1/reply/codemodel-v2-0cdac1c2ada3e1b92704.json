{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-f7fd69275594c9e23612.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "turtle_bot_slam", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "turtle_bot_slam_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-turtle_bot_slam_uninstall-b486393f1da110624a30.json", "name": "turtle_bot_slam_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-9b536e351613bbf00a04.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/turtle/build/turtle_bot_slam", "source": "/home/<USER>/turtle/src/turtle_bot_slam"}, "version": {"major": 2, "minor": 3}}