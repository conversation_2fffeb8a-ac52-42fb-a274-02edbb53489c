<?xml version="1.0" ?>
<sdf version="1.6">
  <world name="turtle_world">

    <include>
      <uri>model://ground_plane</uri>
    </include>

    <include>
      <uri>model://sun</uri>
    </include>

    <!-- A simple house-like environment -->
    <model name="house">
      <static>true</static>
      <link name="walls">
        <!-- Outer walls -->
        <visual name="wall1">
          <pose>5 0 1 0 0 0</pose>
          <geometry>
            <box>
              <size>0.2 10 2</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Bricks</name>
            </script>
          </material>
        </visual>
        <collision name="wall1_collision">
          <pose>5 0 1 0 0 0</pose>
          <geometry>
            <box>
              <size>0.2 10 2</size>
            </box>
          </geometry>
        </collision>

        <visual name="wall2">
          <pose>-5 0 1 0 0 0</pose>
          <geometry>
            <box>
              <size>0.2 10 2</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Bricks</name>
            </script>
          </material>
        </visual>
        <collision name="wall2_collision">
          <pose>-5 0 1 0 0 0</pose>
          <geometry>
            <box>
              <size>0.2 10 2</size>
            </box>
          </geometry>
        </collision>

        <visual name="wall3">
          <pose>0 5 1 0 0 0</pose>
          <geometry>
            <box>
              <size>10 0.2 2</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Bricks</name>
            </script>
          </material>
        </visual>
        <collision name="wall3_collision">
          <pose>0 5 1 0 0 0</pose>
          <geometry>
            <box>
              <size>10 0.2 2</size>
            </box>
          </geometry>
        </collision>

        <visual name="wall4">
          <pose>0 -5 1 0 0 0</pose>
          <geometry>
            <box>
              <size>10 0.2 2</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Bricks</name>
            </script>
          </material>
        </visual>
        <collision name="wall4_collision">
          <pose>0 -5 1 0 0 0</pose>
          <geometry>
            <box>
              <size>10 0.2 2</size>
            </box>
          </geometry>
        </collision>

        <!-- Inner obstacles -->
        <visual name="obstacle1">
          <pose>2 2 0.5 0 0 0</pose>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Wood</name>
            </script>
          </material>
        </visual>
        <collision name="obstacle1_collision">
          <pose>2 2 0.5 0 0 0</pose>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
        </collision>

        <visual name="obstacle2">
          <pose>-2 -2 0.5 0 0 0</pose>
          <geometry>
            <box>
              <size>1.5 0.5 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Wood</name>
            </script>
          </material>
        </visual>
        <collision name="obstacle2_collision">
          <pose>-2 -2 0.5 0 0 0</pose>
          <geometry>
            <box>
              <size>1.5 0.5 1</size>
            </box>
          </geometry>
        </collision>

        <visual name="obstacle3">
          <pose>-1 3 0.5 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Red</name>
            </script>
          </material>
        </visual>
        <collision name="obstacle3_collision">
          <pose>-1 3 0.5 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Physics settings -->
    <physics name="default_physics" default="0" type="ode">
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>150</iters>
          <sor>1.3</sor>
        </solver>
        <constraints>
          <cfm>0.00001</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>1000</contact_max_correcting_vel>
          <contact_surface_layer>0.01</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
    </physics>

    <plugin name="gazebo_ros_state" filename="libgazebo_ros_state.so">
      <ros>
        <namespace>/</namespace>
        <remapping>gazebo_ros_state:=gazebo_ros_state</remapping>
      </ros>
      <update_rate>1.0</update_rate>
    </plugin>

  </world>
</sdf>
