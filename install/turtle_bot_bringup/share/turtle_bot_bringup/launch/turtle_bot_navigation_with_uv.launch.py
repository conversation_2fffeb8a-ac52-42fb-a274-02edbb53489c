#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    """
    Enhanced launch file for navigation with UV disinfection system
    Includes: Gazebo simulation + RViz + Navigation stack + UV system
    """

    # Get package directories
    bringup_dir = get_package_share_directory('turtle_bot_bringup')
    nav_dir = get_package_share_directory('turtle_bot_navigation')
    uv_dir = get_package_share_directory('turtle_bot_uv_system')

    # Launch configuration variables
    map_yaml_file = LaunchConfiguration('map')
    use_sim_time = LaunchConfiguration('use_sim_time')
    params_file = LaunchConfiguration('params_file')
    autostart = LaunchConfiguration('autostart')
    uv_exposure_time = LaunchConfiguration('uv_exposure_time')
    uv_light_range = LaunchConfiguration('uv_light_range')

    # Declare launch arguments
    declare_map_yaml_cmd = DeclareLaunchArgument(
        'map',
        default_value=os.path.join(nav_dir, 'maps', 'map.yaml'),
        description='Full path to map yaml file to load')

    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation (Gazebo) clock if true')

    declare_params_file_cmd = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(nav_dir, 'config', 'nav2_params.yaml'),
        description='Full path to the ROS2 parameters file to use')

    declare_autostart_cmd = DeclareLaunchArgument(
        'autostart', 
        default_value='true',
        description='Automatically startup the nav2 stack')

    declare_uv_exposure_time_cmd = DeclareLaunchArgument(
        'uv_exposure_time',
        default_value='5.0',
        description='Time in seconds needed for full UV disinfection')

    declare_uv_light_range_cmd = DeclareLaunchArgument(
        'uv_light_range',
        default_value='1.5',
        description='Range of UV light in meters')

    # Include the simulation launch file (Gazebo + RViz)
    simulation_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(bringup_dir, 'launch'),
            '/turtle_bot_simulation.launch.py'
        ])
    )

    # Include the navigation launch file
    navigation_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(nav_dir, 'launch'),
            '/navigation.launch.py'
        ]),
        launch_arguments={
            'map': map_yaml_file,
            'use_sim_time': use_sim_time,
            'params_file': params_file,
            'autostart': autostart
        }.items()
    )

    # Include the UV system launch file
    uv_system_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(uv_dir, 'launch'),
            '/uv_system.launch.py'
        ]),
        launch_arguments={
            'uv_exposure_time': uv_exposure_time,
            'uv_light_range': uv_light_range,
            'auto_save_interval': '30.0',
            'save_file': '~/turtle_uv_exposure_data.json'
        }.items()
    )

    # Delay navigation startup to allow simulation to initialize
    delayed_navigation = TimerAction(
        period=10.0,
        actions=[navigation_launch]
    )

    # Delay UV system startup to allow navigation to initialize
    delayed_uv_system = TimerAction(
        period=15.0,
        actions=[uv_system_launch]
    )

    return LaunchDescription([
        # Declare launch arguments
        declare_map_yaml_cmd,
        declare_use_sim_time_cmd,
        declare_params_file_cmd,
        declare_autostart_cmd,
        declare_uv_exposure_time_cmd,
        declare_uv_light_range_cmd,
        
        # Launch simulation first
        simulation_launch,
        
        # Then launch navigation after delay
        delayed_navigation,
        
        # Finally launch UV system
        delayed_uv_system,
    ])
