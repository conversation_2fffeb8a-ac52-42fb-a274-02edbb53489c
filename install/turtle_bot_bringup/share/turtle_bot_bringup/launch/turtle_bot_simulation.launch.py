#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node


def generate_launch_description():

    # Include the Gazebo launch file
    gazebo_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(get_package_share_directory('turtle_bot_gazebo'), 'launch'),
            '/gazebo.launch.py'
        ])
    )

    # Launch RViz
    rviz_node = Node(
        package='turtle_bot_bringup',
        executable='launch_rviz.py',
        name='rviz_launcher',
        parameters=[{'use_sim_time': True}],
        output='screen',
        respawn=False
    )

    # Delay RViz startup to allow Gazebo to initialize
    delayed_rviz = TimerAction(
        period=5.0,
        actions=[rviz_node]
    )

    return LaunchDescription([
        gazebo_launch,
        delayed_rviz,
    ])
