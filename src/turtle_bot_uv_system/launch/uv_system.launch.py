#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, Node
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    """
    Launch file for the complete UV disinfection system
    """

    # Launch configuration variables
    uv_exposure_time = LaunchConfiguration('uv_exposure_time')
    uv_light_range = LaunchConfiguration('uv_light_range')
    auto_save_interval = LaunchConfiguration('auto_save_interval')
    save_file = LaunchConfiguration('save_file')

    # Declare launch arguments
    declare_uv_exposure_time_cmd = DeclareLaunchArgument(
        'uv_exposure_time',
        default_value='5.0',
        description='Time in seconds needed for full UV disinfection')

    declare_uv_light_range_cmd = DeclareLaunchArgument(
        'uv_light_range',
        default_value='1.5',
        description='Range of UV light in meters')

    declare_auto_save_interval_cmd = DeclareLaunchArgument(
        'auto_save_interval',
        default_value='30.0',
        description='Auto-save interval for UV data in seconds')

    declare_save_file_cmd = DeclareLaunchArgument(
        'save_file',
        default_value='~/turtle_uv_exposure_data.json',
        description='File path for saving UV exposure data')

    # UV Light Controller Node
    uv_controller_node = Node(
        package='turtle_bot_uv_system',
        executable='uv_light_controller',
        name='uv_light_controller',
        parameters=[{
            'uv_exposure_time': uv_exposure_time,
            'uv_light_range': uv_light_range,
            'update_rate': 10.0,
            'map_resolution': 0.05
        }],
        output='screen'
    )

    # UV Map Tracker Node
    uv_tracker_node = Node(
        package='turtle_bot_uv_system',
        executable='uv_map_tracker',
        name='uv_map_tracker',
        parameters=[{
            'save_file': save_file,
            'auto_save_interval': auto_save_interval,
            'uv_exposure_threshold': uv_exposure_time,
            'decay_rate': 0.0  # No decay by default
        }],
        output='screen'
    )

    # UV Visualizer Node
    uv_visualizer_node = Node(
        package='turtle_bot_uv_system',
        executable='uv_visualizer',
        name='uv_visualizer',
        parameters=[{
            'visualization_height': 0.01,
            'marker_scale': 0.05,
            'update_rate': 2.0
        }],
        output='screen'
    )

    return LaunchDescription([
        # Declare launch arguments
        declare_uv_exposure_time_cmd,
        declare_uv_light_range_cmd,
        declare_auto_save_interval_cmd,
        declare_save_file_cmd,

        # Launch UV system nodes
        uv_controller_node,
        uv_tracker_node,
        uv_visualizer_node,
    ])
