#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import <PERSON><PERSON>, <PERSON>loat<PERSON>, <PERSON><PERSON>
from geometry_msgs.msg import PoseStamped, PointStamped
from sensor_msgs.msg import Image
from nav_msgs.msg import OccupancyGrid
import tf2_ros
import tf2_geometry_msgs
import numpy as np
import time
from threading import Lock


class UVLightController(Node):
    """
    UV Light Controller Node
    
    This node controls the UV light state and tracks UV exposure on the map.
    It publishes UV exposure data and manages the UV disinfection process.
    """

    def __init__(self):
        super().__init__('uv_light_controller')
        
        # Parameters
        self.declare_parameter('uv_exposure_time', 5.0)  # seconds needed for disinfection
        self.declare_parameter('uv_light_range', 1.5)    # meters
        self.declare_parameter('map_resolution', 0.05)   # meters per pixel
        self.declare_parameter('update_rate', 10.0)      # Hz
        
        self.uv_exposure_time = self.get_parameter('uv_exposure_time').value
        self.uv_light_range = self.get_parameter('uv_light_range').value
        self.map_resolution = self.get_parameter('map_resolution').value
        self.update_rate = self.get_parameter('update_rate').value
        
        # UV light state
        self.uv_light_enabled = False
        self.uv_exposure_map = None  # Will store UV exposure times for each map cell
        self.current_map = None
        self.robot_pose = None
        
        # Thread safety
        self.lock = Lock()
        
        # TF2 setup
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)
        
        # Publishers
        self.uv_state_pub = self.create_publisher(Bool, 'uv_light/state', 10)
        self.uv_exposure_pub = self.create_publisher(OccupancyGrid, 'uv_light/exposure_map', 10)
        self.uv_coverage_pub = self.create_publisher(Float32, 'uv_light/coverage_percentage', 10)
        
        # Subscribers
        self.uv_control_sub = self.create_subscription(
            Bool, 'uv_light/control', self.uv_control_callback, 10)
        self.map_sub = self.create_subscription(
            OccupancyGrid, 'map', self.map_callback, 10)
        self.uv_sensor_sub = self.create_subscription(
            Image, 'uv_light/image', self.uv_sensor_callback, 10)
        
        # Timer for UV exposure tracking
        self.timer = self.create_timer(1.0 / self.update_rate, self.update_uv_exposure)
        
        self.get_logger().info("UV Light Controller initialized")
        self.get_logger().info(f"UV exposure time: {self.uv_exposure_time}s")
        self.get_logger().info(f"UV light range: {self.uv_light_range}m")

    def uv_control_callback(self, msg):
        """Handle UV light control commands"""
        with self.lock:
            self.uv_light_enabled = msg.data
            
        # Publish current state
        state_msg = Bool()
        state_msg.data = self.uv_light_enabled
        self.uv_state_pub.publish(state_msg)
        
        status = "ON" if self.uv_light_enabled else "OFF"
        self.get_logger().info(f"UV Light turned {status}")

    def map_callback(self, msg):
        """Handle new map data"""
        with self.lock:
            self.current_map = msg
            
            # Initialize UV exposure map if needed
            if self.uv_exposure_map is None or \
               self.uv_exposure_map.shape != (msg.info.height, msg.info.width):
                self.uv_exposure_map = np.zeros((msg.info.height, msg.info.width), dtype=np.float32)
                self.get_logger().info(f"Initialized UV exposure map: {msg.info.width}x{msg.info.height}")

    def uv_sensor_callback(self, msg):
        """Handle UV sensor data (placeholder for future sensor integration)"""
        # This could be used for actual UV sensor feedback in the future
        pass

    def get_robot_pose(self):
        """Get current robot pose in map frame"""
        try:
            # Get transform from map to base_link
            transform = self.tf_buffer.lookup_transform(
                'map', 'base_link', rclpy.time.Time())
            
            pose = PoseStamped()
            pose.header.frame_id = 'map'
            pose.header.stamp = transform.header.stamp
            pose.pose.position.x = transform.transform.translation.x
            pose.pose.position.y = transform.transform.translation.y
            pose.pose.position.z = transform.transform.translation.z
            pose.pose.orientation = transform.transform.rotation
            
            return pose
            
        except Exception as e:
            self.get_logger().debug(f"Could not get robot pose: {e}")
            return None

    def update_uv_exposure(self):
        """Update UV exposure map based on current robot position and UV light state"""
        if not self.uv_light_enabled or self.current_map is None or self.uv_exposure_map is None:
            return
            
        # Get current robot pose
        robot_pose = self.get_robot_pose()
        if robot_pose is None:
            return
            
        with self.lock:
            # Convert robot position to map coordinates
            map_x = int((robot_pose.pose.position.x - self.current_map.info.origin.position.x) / 
                       self.current_map.info.resolution)
            map_y = int((robot_pose.pose.position.y - self.current_map.info.origin.position.y) / 
                       self.current_map.info.resolution)
            
            # Calculate UV light coverage area
            range_cells = int(self.uv_light_range / self.current_map.info.resolution)
            
            # Update exposure for cells within UV light range
            for dy in range(-range_cells, range_cells + 1):
                for dx in range(-range_cells, range_cells + 1):
                    cell_x = map_x + dx
                    cell_y = map_y + dy
                    
                    # Check bounds
                    if (0 <= cell_x < self.current_map.info.width and 
                        0 <= cell_y < self.current_map.info.height):
                        
                        # Calculate distance from robot
                        distance = np.sqrt(dx*dx + dy*dy) * self.current_map.info.resolution
                        
                        if distance <= self.uv_light_range:
                            # Add exposure time (scaled by distance for realistic effect)
                            exposure_increment = (1.0 / self.update_rate) * (1.0 - distance / self.uv_light_range)
                            self.uv_exposure_map[cell_y, cell_x] += exposure_increment
            
            # Publish updated exposure map
            self.publish_exposure_map()
            self.publish_coverage_stats()

    def publish_exposure_map(self):
        """Publish the UV exposure map as an OccupancyGrid"""
        if self.current_map is None or self.uv_exposure_map is None:
            return
            
        exposure_msg = OccupancyGrid()
        exposure_msg.header = Header()
        exposure_msg.header.stamp = self.get_clock().now().to_msg()
        exposure_msg.header.frame_id = 'map'
        exposure_msg.info = self.current_map.info
        
        # Convert exposure times to occupancy grid values (0-100)
        # Green areas (fully disinfected) = 100, unexposed = 0
        normalized_exposure = np.clip(self.uv_exposure_map / self.uv_exposure_time * 100, 0, 100)
        exposure_msg.data = normalized_exposure.flatten().astype(np.int8).tolist()
        
        self.uv_exposure_pub.publish(exposure_msg)

    def publish_coverage_stats(self):
        """Publish UV coverage statistics"""
        if self.uv_exposure_map is None:
            return
            
        # Calculate percentage of map that has been fully disinfected
        fully_disinfected = np.sum(self.uv_exposure_map >= self.uv_exposure_time)
        total_cells = self.uv_exposure_map.size
        coverage_percentage = (fully_disinfected / total_cells) * 100.0
        
        coverage_msg = Float32()
        coverage_msg.data = coverage_percentage
        self.uv_coverage_pub.publish(coverage_msg)


def main(args=None):
    rclpy.init(args=args)
    
    uv_controller = UVLightController()
    
    try:
        rclpy.spin(uv_controller)
    except KeyboardInterrupt:
        pass
    finally:
        uv_controller.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
