#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import Head<PERSON>, ColorRGBA
from geometry_msgs.msg import Point
from visualization_msgs.msg import Marker, MarkerArray
from nav_msgs.msg import OccupancyGrid
import numpy as np
from threading import Lock


class UVVisualizer(Node):
    """
    UV Visualizer Node
    
    This node creates visual representations of UV exposure data for RViz.
    It publishes markers and colored overlays to show disinfected areas.
    """

    def __init__(self):
        super().__init__('uv_visualizer')
        
        # Parameters
        self.declare_parameter('visualization_height', 0.01)  # Height above ground for markers
        self.declare_parameter('marker_scale', 0.05)  # Size of individual markers
        self.declare_parameter('update_rate', 2.0)  # Hz for visualization updates
        
        self.viz_height = self.get_parameter('visualization_height').value
        self.marker_scale = self.get_parameter('marker_scale').value
        self.update_rate = self.get_parameter('update_rate').value
        
        # Data storage
        self.current_map = None
        self.exposure_map = None
        self.disinfection_status = None
        
        # Thread safety
        self.lock = Lock()
        
        # Publishers
        self.uv_markers_pub = self.create_publisher(
            MarkerArray, 'uv_visualization/markers', 10)
        self.uv_overlay_pub = self.create_publisher(
            Marker, 'uv_visualization/overlay', 10)
        self.uv_grid_pub = self.create_publisher(
            MarkerArray, 'uv_visualization/grid', 10)
        
        # Subscribers
        self.map_sub = self.create_subscription(
            OccupancyGrid, 'map', self.map_callback, 10)
        self.exposure_sub = self.create_subscription(
            OccupancyGrid, 'uv_tracker/persistent_exposure_map', self.exposure_callback, 10)
        self.status_sub = self.create_subscription(
            OccupancyGrid, 'uv_tracker/disinfection_status', self.status_callback, 10)
        
        # Timer for periodic visualization updates
        self.timer = self.create_timer(1.0 / self.update_rate, self.update_visualization)
        
        self.get_logger().info("UV Visualizer initialized")

    def map_callback(self, msg):
        """Handle new map data"""
        with self.lock:
            self.current_map = msg

    def exposure_callback(self, msg):
        """Handle UV exposure map updates"""
        with self.lock:
            # Convert exposure data to numpy array
            self.exposure_map = np.array(msg.data, dtype=np.float32).reshape(
                (msg.info.height, msg.info.width))

    def status_callback(self, msg):
        """Handle disinfection status updates"""
        with self.lock:
            # Convert status data to numpy array
            self.disinfection_status = np.array(msg.data, dtype=np.int8).reshape(
                (msg.info.height, msg.info.width))

    def update_visualization(self):
        """Update all visualizations"""
        if self.current_map is None:
            return
            
        with self.lock:
            self.publish_uv_grid_markers()
            self.publish_uv_overlay()
            self.publish_disinfection_markers()

    def publish_uv_grid_markers(self):
        """Publish grid markers showing UV exposure levels"""
        if self.exposure_map is None:
            return
            
        marker_array = MarkerArray()
        
        # Clear previous markers
        clear_marker = Marker()
        clear_marker.header.frame_id = 'map'
        clear_marker.header.stamp = self.get_clock().now().to_msg()
        clear_marker.action = Marker.DELETEALL
        marker_array.markers.append(clear_marker)
        
        # Create markers for exposed areas
        marker_id = 0
        for y in range(0, self.exposure_map.shape[0], 2):  # Skip every other cell for performance
            for x in range(0, self.exposure_map.shape[1], 2):
                exposure_level = self.exposure_map[y, x]
                
                if exposure_level > 0:  # Only show areas with some exposure
                    marker = Marker()
                    marker.header.frame_id = 'map'
                    marker.header.stamp = self.get_clock().now().to_msg()
                    marker.ns = 'uv_exposure'
                    marker.id = marker_id
                    marker.type = Marker.CUBE
                    marker.action = Marker.ADD
                    
                    # Calculate world position
                    world_x = (x * self.current_map.info.resolution + 
                              self.current_map.info.origin.position.x)
                    world_y = (y * self.current_map.info.resolution + 
                              self.current_map.info.origin.position.y)
                    
                    marker.pose.position.x = world_x
                    marker.pose.position.y = world_y
                    marker.pose.position.z = self.viz_height
                    marker.pose.orientation.w = 1.0
                    
                    marker.scale.x = self.marker_scale
                    marker.scale.y = self.marker_scale
                    marker.scale.z = 0.005  # Very thin
                    
                    # Color based on exposure level (0-100%)
                    exposure_ratio = min(exposure_level / 100.0, 1.0)
                    
                    # Gradient from red (low exposure) to green (high exposure)
                    marker.color.r = 1.0 - exposure_ratio
                    marker.color.g = exposure_ratio
                    marker.color.b = 0.0
                    marker.color.a = 0.7
                    
                    marker.lifetime.sec = 1
                    marker_array.markers.append(marker)
                    marker_id += 1
        
        self.uv_grid_pub.publish(marker_array)

    def publish_uv_overlay(self):
        """Publish a semi-transparent overlay showing UV coverage"""
        if self.exposure_map is None:
            return
            
        # Create a single large marker as overlay
        overlay = Marker()
        overlay.header.frame_id = 'map'
        overlay.header.stamp = self.get_clock().now().to_msg()
        overlay.ns = 'uv_overlay'
        overlay.id = 0
        overlay.type = Marker.TRIANGLE_LIST
        overlay.action = Marker.ADD
        
        # Create triangles for areas with UV exposure
        points = []
        colors = []
        
        cell_size = self.current_map.info.resolution
        
        for y in range(self.exposure_map.shape[0] - 1):
            for x in range(self.exposure_map.shape[1] - 1):
                exposure = self.exposure_map[y, x]
                
                if exposure > 0:
                    # Calculate world coordinates for cell corners
                    x1 = x * cell_size + self.current_map.info.origin.position.x
                    y1 = y * cell_size + self.current_map.info.origin.position.y
                    x2 = (x + 1) * cell_size + self.current_map.info.origin.position.x
                    y2 = (y + 1) * cell_size + self.current_map.info.origin.position.y
                    
                    # Create two triangles for the cell
                    # Triangle 1
                    p1 = Point(x=x1, y=y1, z=self.viz_height)
                    p2 = Point(x=x2, y=y1, z=self.viz_height)
                    p3 = Point(x=x1, y=y2, z=self.viz_height)
                    
                    # Triangle 2
                    p4 = Point(x=x2, y=y1, z=self.viz_height)
                    p5 = Point(x=x2, y=y2, z=self.viz_height)
                    p6 = Point(x=x1, y=y2, z=self.viz_height)
                    
                    points.extend([p1, p2, p3, p4, p5, p6])
                    
                    # Color based on exposure level
                    exposure_ratio = min(exposure / 100.0, 1.0)
                    color = ColorRGBA()
                    color.r = 1.0 - exposure_ratio
                    color.g = exposure_ratio
                    color.b = 0.0
                    color.a = 0.3
                    
                    # Same color for all 6 vertices of both triangles
                    colors.extend([color] * 6)
        
        overlay.points = points
        overlay.colors = colors
        overlay.scale.x = 1.0
        overlay.scale.y = 1.0
        overlay.scale.z = 1.0
        overlay.lifetime.sec = 1
        
        self.uv_overlay_pub.publish(overlay)

    def publish_disinfection_markers(self):
        """Publish markers for fully disinfected areas"""
        if self.disinfection_status is None:
            return
            
        marker_array = MarkerArray()
        
        # Clear previous markers
        clear_marker = Marker()
        clear_marker.header.frame_id = 'map'
        clear_marker.header.stamp = self.get_clock().now().to_msg()
        clear_marker.ns = 'disinfected_areas'
        clear_marker.action = Marker.DELETEALL
        marker_array.markers.append(clear_marker)
        
        # Create markers for fully disinfected areas
        marker_id = 0
        for y in range(0, self.disinfection_status.shape[0], 3):  # Sparse sampling
            for x in range(0, self.disinfection_status.shape[1], 3):
                if self.disinfection_status[y, x] == 100:  # Fully disinfected
                    marker = Marker()
                    marker.header.frame_id = 'map'
                    marker.header.stamp = self.get_clock().now().to_msg()
                    marker.ns = 'disinfected_areas'
                    marker.id = marker_id
                    marker.type = Marker.CYLINDER
                    marker.action = Marker.ADD
                    
                    # Calculate world position
                    world_x = (x * self.current_map.info.resolution + 
                              self.current_map.info.origin.position.x)
                    world_y = (y * self.current_map.info.resolution + 
                              self.current_map.info.origin.position.y)
                    
                    marker.pose.position.x = world_x
                    marker.pose.position.y = world_y
                    marker.pose.position.z = self.viz_height + 0.01
                    marker.pose.orientation.w = 1.0
                    
                    marker.scale.x = self.marker_scale * 1.5
                    marker.scale.y = self.marker_scale * 1.5
                    marker.scale.z = 0.01
                    
                    # Bright green for disinfected areas
                    marker.color.r = 0.0
                    marker.color.g = 1.0
                    marker.color.b = 0.0
                    marker.color.a = 0.8
                    
                    marker.lifetime.sec = 1
                    marker_array.markers.append(marker)
                    marker_id += 1
        
        self.uv_markers_pub.publish(marker_array)


def main(args=None):
    rclpy.init(args=args)
    
    uv_visualizer = UVVisualizer()
    
    try:
        rclpy.spin(uv_visualizer)
    except KeyboardInterrupt:
        pass
    finally:
        uv_visualizer.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
