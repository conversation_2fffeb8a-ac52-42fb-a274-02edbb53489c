#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import <PERSON><PERSON>, <PERSON>loat<PERSON>, <PERSON><PERSON>
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import OccupancyGrid
import numpy as np
import json
import os
from threading import Lock
import time


class UVMapTracker(Node):
    """
    UV Map Tracker Node
    
    This node provides persistent tracking of UV exposure across map sessions.
    It saves and loads UV exposure data and provides advanced analytics.
    """

    def __init__(self):
        super().__init__('uv_map_tracker')
        
        # Parameters
        self.declare_parameter('save_file', '~/turtle_uv_exposure_data.json')
        self.declare_parameter('auto_save_interval', 30.0)  # seconds
        self.declare_parameter('uv_exposure_threshold', 5.0)  # seconds for full disinfection
        self.declare_parameter('decay_rate', 0.0)  # UV exposure decay per hour (0 = no decay)
        
        self.save_file = os.path.expanduser(self.get_parameter('save_file').value)
        self.auto_save_interval = self.get_parameter('auto_save_interval').value
        self.uv_exposure_threshold = self.get_parameter('uv_exposure_threshold').value
        self.decay_rate = self.get_parameter('decay_rate').value
        
        # Data storage
        self.uv_exposure_data = {}  # Map hash -> exposure data
        self.current_map_hash = None
        self.current_exposure_map = None
        self.last_save_time = time.time()
        
        # Thread safety
        self.lock = Lock()
        
        # Publishers
        self.persistent_exposure_pub = self.create_publisher(
            OccupancyGrid, 'uv_tracker/persistent_exposure_map', 10)
        self.disinfection_status_pub = self.create_publisher(
            OccupancyGrid, 'uv_tracker/disinfection_status', 10)
        self.total_coverage_pub = self.create_publisher(
            Float32, 'uv_tracker/total_coverage', 10)
        
        # Subscribers
        self.map_sub = self.create_subscription(
            OccupancyGrid, 'map', self.map_callback, 10)
        self.exposure_sub = self.create_subscription(
            OccupancyGrid, 'uv_light/exposure_map', self.exposure_callback, 10)
        
        # Services for manual save/load
        self.create_service(
            rclpy.srv.Empty, 'uv_tracker/save_data', self.save_data_service)
        self.create_service(
            rclpy.srv.Empty, 'uv_tracker/load_data', self.load_data_service)
        self.create_service(
            rclpy.srv.Empty, 'uv_tracker/reset_data', self.reset_data_service)
        
        # Timer for auto-save and decay processing
        self.timer = self.create_timer(10.0, self.periodic_update)
        
        # Load existing data
        self.load_data()
        
        self.get_logger().info("UV Map Tracker initialized")
        self.get_logger().info(f"Save file: {self.save_file}")
        self.get_logger().info(f"Auto-save interval: {self.auto_save_interval}s")

    def calculate_map_hash(self, map_msg):
        """Calculate a hash for the map to identify unique maps"""
        # Use map dimensions, resolution, and origin as identifier
        map_info = f"{map_msg.info.width}x{map_msg.info.height}_{map_msg.info.resolution}_{map_msg.info.origin.position.x}_{map_msg.info.origin.position.y}"
        return hash(map_info)

    def map_callback(self, msg):
        """Handle new map data"""
        with self.lock:
            new_hash = self.calculate_map_hash(msg)
            
            if self.current_map_hash != new_hash:
                self.get_logger().info(f"New map detected (hash: {new_hash})")
                self.current_map_hash = new_hash
                
                # Initialize exposure data for this map if not exists
                if new_hash not in self.uv_exposure_data:
                    self.uv_exposure_data[new_hash] = {
                        'exposure_map': np.zeros((msg.info.height, msg.info.width), dtype=np.float32),
                        'map_info': {
                            'width': msg.info.width,
                            'height': msg.info.height,
                            'resolution': msg.info.resolution,
                            'origin_x': msg.info.origin.position.x,
                            'origin_y': msg.info.origin.position.y
                        },
                        'last_updated': time.time(),
                        'total_exposure_time': 0.0
                    }
                    self.get_logger().info(f"Initialized new exposure map for hash {new_hash}")
                else:
                    self.get_logger().info(f"Loaded existing exposure data for hash {new_hash}")
                
                self.current_exposure_map = self.uv_exposure_data[new_hash]['exposure_map']
                self.publish_persistent_maps()

    def exposure_callback(self, msg):
        """Handle UV exposure updates from the controller"""
        if self.current_map_hash is None or self.current_exposure_map is None:
            return
            
        with self.lock:
            # Convert exposure map data back to numpy array
            exposure_array = np.array(msg.data, dtype=np.float32).reshape(
                (msg.info.height, msg.info.width))
            
            # Update persistent exposure data (accumulate exposure)
            # Convert from percentage back to time
            exposure_time = exposure_array * self.uv_exposure_threshold / 100.0
            
            # Take maximum exposure (areas can only get more disinfected, not less)
            self.current_exposure_map = np.maximum(self.current_exposure_map, exposure_time)
            
            # Update metadata
            self.uv_exposure_data[self.current_map_hash]['exposure_map'] = self.current_exposure_map
            self.uv_exposure_data[self.current_map_hash]['last_updated'] = time.time()
            
            # Publish updated persistent maps
            self.publish_persistent_maps()

    def apply_decay(self):
        """Apply UV exposure decay over time"""
        if self.decay_rate <= 0:
            return
            
        current_time = time.time()
        
        with self.lock:
            for map_hash, data in self.uv_exposure_data.items():
                last_updated = data['last_updated']
                hours_elapsed = (current_time - last_updated) / 3600.0
                
                if hours_elapsed > 0:
                    # Apply exponential decay
                    decay_factor = np.exp(-self.decay_rate * hours_elapsed)
                    data['exposure_map'] *= decay_factor
                    data['last_updated'] = current_time

    def publish_persistent_maps(self):
        """Publish persistent exposure and disinfection status maps"""
        if self.current_map_hash is None or self.current_exposure_map is None:
            return
            
        map_info = self.uv_exposure_data[self.current_map_hash]['map_info']
        
        # Create OccupancyGrid info
        grid_info = OccupancyGrid().info
        grid_info.width = map_info['width']
        grid_info.height = map_info['height']
        grid_info.resolution = map_info['resolution']
        grid_info.origin.position.x = map_info['origin_x']
        grid_info.origin.position.y = map_info['origin_y']
        
        # Publish persistent exposure map
        exposure_msg = OccupancyGrid()
        exposure_msg.header = Header()
        exposure_msg.header.stamp = self.get_clock().now().to_msg()
        exposure_msg.header.frame_id = 'map'
        exposure_msg.info = grid_info
        
        # Convert to percentage (0-100)
        exposure_percentage = np.clip(
            self.current_exposure_map / self.uv_exposure_threshold * 100, 0, 100)
        exposure_msg.data = exposure_percentage.flatten().astype(np.int8).tolist()
        self.persistent_exposure_pub.publish(exposure_msg)
        
        # Publish disinfection status (binary: disinfected or not)
        status_msg = OccupancyGrid()
        status_msg.header = exposure_msg.header
        status_msg.info = grid_info
        
        # Binary status: 100 = fully disinfected, 0 = not disinfected
        disinfected = (self.current_exposure_map >= self.uv_exposure_threshold).astype(np.int8) * 100
        status_msg.data = disinfected.flatten().tolist()
        self.disinfection_status_pub.publish(status_msg)
        
        # Publish total coverage statistics
        total_cells = self.current_exposure_map.size
        disinfected_cells = np.sum(self.current_exposure_map >= self.uv_exposure_threshold)
        coverage_percentage = (disinfected_cells / total_cells) * 100.0
        
        coverage_msg = Float32()
        coverage_msg.data = coverage_percentage
        self.total_coverage_pub.publish(coverage_msg)

    def periodic_update(self):
        """Periodic update for auto-save and decay"""
        current_time = time.time()
        
        # Apply decay if enabled
        if self.decay_rate > 0:
            self.apply_decay()
            
        # Auto-save if interval has passed
        if current_time - self.last_save_time > self.auto_save_interval:
            self.save_data()
            self.last_save_time = current_time

    def save_data(self):
        """Save UV exposure data to file"""
        try:
            with self.lock:
                # Convert numpy arrays to lists for JSON serialization
                save_data = {}
                for map_hash, data in self.uv_exposure_data.items():
                    save_data[str(map_hash)] = {
                        'exposure_map': data['exposure_map'].tolist(),
                        'map_info': data['map_info'],
                        'last_updated': data['last_updated'],
                        'total_exposure_time': float(np.sum(data['exposure_map']))
                    }
                
                # Ensure directory exists
                os.makedirs(os.path.dirname(self.save_file), exist_ok=True)
                
                with open(self.save_file, 'w') as f:
                    json.dump(save_data, f, indent=2)
                    
            self.get_logger().info(f"UV exposure data saved to {self.save_file}")
            
        except Exception as e:
            self.get_logger().error(f"Failed to save UV data: {e}")

    def load_data(self):
        """Load UV exposure data from file"""
        try:
            if os.path.exists(self.save_file):
                with open(self.save_file, 'r') as f:
                    save_data = json.load(f)
                
                with self.lock:
                    self.uv_exposure_data = {}
                    for map_hash_str, data in save_data.items():
                        map_hash = int(map_hash_str)
                        self.uv_exposure_data[map_hash] = {
                            'exposure_map': np.array(data['exposure_map'], dtype=np.float32),
                            'map_info': data['map_info'],
                            'last_updated': data['last_updated'],
                            'total_exposure_time': data.get('total_exposure_time', 0.0)
                        }
                
                self.get_logger().info(f"UV exposure data loaded from {self.save_file}")
                self.get_logger().info(f"Loaded data for {len(self.uv_exposure_data)} maps")
            else:
                self.get_logger().info("No existing UV data file found, starting fresh")
                
        except Exception as e:
            self.get_logger().error(f"Failed to load UV data: {e}")

    def save_data_service(self, request, response):
        """Service to manually save data"""
        self.save_data()
        return response

    def load_data_service(self, request, response):
        """Service to manually load data"""
        self.load_data()
        return response

    def reset_data_service(self, request, response):
        """Service to reset all UV data"""
        with self.lock:
            self.uv_exposure_data = {}
            self.current_exposure_map = None
        self.get_logger().info("UV exposure data reset")
        return response


def main(args=None):
    rclpy.init(args=args)
    
    uv_tracker = UVMapTracker()
    
    try:
        rclpy.spin(uv_tracker)
    except KeyboardInterrupt:
        pass
    finally:
        # Save data before shutdown
        uv_tracker.save_data()
        uv_tracker.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
