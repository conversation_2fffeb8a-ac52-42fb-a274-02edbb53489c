from setuptools import find_packages, setup

package_name = 'turtle_bot_uv_system'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='haythem',
    maintainer_email='<EMAIL>',
    description='TODO: Package description',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'uv_light_controller = turtle_bot_uv_system.uv_light_controller:main',
            'uv_map_tracker = turtle_bot_uv_system.uv_map_tracker:main',
            'uv_visualizer = turtle_bot_uv_system.uv_visualizer:main',
        ],
    },
)
