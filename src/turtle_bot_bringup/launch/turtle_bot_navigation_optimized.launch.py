#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    # Get the launch directory
    bringup_dir = get_package_share_directory('turtle_bot_bringup')
    navigation_dir = get_package_share_directory('turtle_bot_navigation')
    
    # Create the launch configuration variables
    map_yaml_file = LaunchConfiguration('map')
    use_sim_time = LaunchConfiguration('use_sim_time')
    params_file = LaunchConfiguration('params_file')
    autostart = LaunchConfiguration('autostart')

    # Declare the launch arguments
    declare_map_yaml_cmd = DeclareLaunchArgument(
        'map',
        default_value=os.path.join(navigation_dir, 'maps', 'map.yaml'),
        description='Full path to map yaml file to load')

    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation (Gazebo) clock if true')

    declare_params_file_cmd = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(navigation_dir, 'config', 'nav2_params_optimized.yaml'),
        description='Full path to the ROS2 parameters file to use for all launched nodes')

    declare_autostart_cmd = DeclareLaunchArgument(
        'autostart', 
        default_value='true',
        description='Automatically startup the nav2 stack')

    # Launch simulation
    simulation_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(bringup_dir, 'launch', 'turtle_bot_simulation.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
        }.items()
    )

    # Add static transform between base_link and base_footprint
    base_link_to_base_footprint_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_link_to_base_footprint',
        arguments=['0', '0', '0', '0', '0', '0', 'base_link', 'base_footprint'],
        parameters=[{'use_sim_time': use_sim_time}],
        output='screen'
    )

    # Navigation launch with optimized parameters
    navigation_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(get_package_share_directory('nav2_bringup'), 'launch', 'bringup_launch.py')
        ),
        launch_arguments={
            'map': map_yaml_file,
            'use_sim_time': use_sim_time,
            'params_file': params_file,
            'autostart': autostart
        }.items()
    )

    # Enhanced initial pose publisher for faster initialization
    initial_pose_publisher = Node(
        package='turtle_bot_bringup',
        executable='initial_pose_publisher.py',
        name='initial_pose_publisher',
        parameters=[{'use_sim_time': use_sim_time}],
        output='screen'
    )

    # Delay navigation startup to allow simulation to initialize
    delayed_navigation = TimerAction(
        period=8.0,  # Reduced delay for faster startup
        actions=[navigation_launch]
    )

    # Delay initial pose publication to allow AMCL to start
    delayed_initial_pose = TimerAction(
        period=12.0,  # Reduced delay for faster startup
        actions=[initial_pose_publisher]
    )

    return LaunchDescription([
        # Declare launch arguments
        declare_map_yaml_cmd,
        declare_use_sim_time_cmd,
        declare_params_file_cmd,
        declare_autostart_cmd,
        
        # Launch simulation first
        simulation_launch,
        
        # Add static transform to connect frame trees
        base_link_to_base_footprint_tf,
        
        # Then launch navigation after delay
        delayed_navigation,
        
        # Finally publish initial pose to initialize AMCL
        delayed_initial_pose,
    ])
