#!/usr/bin/env python3

import os
import subprocess
import sys
import time


def launch_rviz():
    """Launch RViz with proper environment setup"""
    
    # Set environment variables to handle Qt/Wayland issues
    env = os.environ.copy()
    env['QT_QPA_PLATFORM'] = 'xcb'  # Force X11 instead of Wayland
    env['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'
    
    # Get the RViz config file path
    config_file = '/home/<USER>/turtle2/src/turtle_bot_bringup/launch/turtle_bot.rviz'
    
    # Check if config file exists
    if not os.path.exists(config_file):
        print(f"Warning: RViz config file not found at {config_file}")
        print("Launching RViz with default configuration...")
        config_file = None
    
    # Build the command
    cmd = ['ros2', 'run', 'rviz2', 'rviz2']
    if config_file:
        cmd.extend(['-d', config_file])
    
    # Add ROS parameters
    cmd.extend(['--ros-args', '-p', 'use_sim_time:=true'])
    
    print(f"Launching RViz with command: {' '.join(cmd)}")
    
    try:
        # Launch RViz
        process = subprocess.Popen(cmd, env=env)
        print(f"RViz launched with PID: {process.pid}")
        
        # Wait for the process to complete
        process.wait()
        
    except KeyboardInterrupt:
        print("RViz launch interrupted by user")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"Error launching RViz: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(launch_rviz())
