#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Po<PERSON>Stamped, Twist
from nav2_simple_commander.robot_navigator import BasicNavigator
import time
import math


class EnhancedNavigationTester(Node):
    def __init__(self):
        super().__init__('enhanced_navigation_tester')
        
        # Initialize the navigator
        self.navigator = BasicNavigator()
        
        # Create velocity publisher for manual control if needed
        self.cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        
        # Wait for navigation to fully activate
        self.get_logger().info('Waiting for navigation to become active...')
        self.navigator.waitUntilNav2Active()
        self.get_logger().info('Navigation is active!')
        
        # Run comprehensive navigation tests
        self.run_navigation_tests()

    def create_goal_pose(self, x, y, yaw=0.0):
        """Create a goal pose with given coordinates and orientation"""
        goal_pose = PoseStamped()
        goal_pose.header.frame_id = 'map'
        goal_pose.header.stamp = self.navigator.get_clock().now().to_msg()
        
        goal_pose.pose.position.x = x
        goal_pose.pose.position.y = y
        goal_pose.pose.position.z = 0.0
        
        # Convert yaw to quaternion
        goal_pose.pose.orientation.x = 0.0
        goal_pose.pose.orientation.y = 0.0
        goal_pose.pose.orientation.z = math.sin(yaw / 2.0)
        goal_pose.pose.orientation.w = math.cos(yaw / 2.0)
        
        return goal_pose

    def navigate_to_goal(self, goal_pose, timeout=60.0):
        """Navigate to a goal and return success status"""
        self.get_logger().info(f'Navigating to goal: x={goal_pose.pose.position.x:.2f}, y={goal_pose.pose.position.y:.2f}')
        
        # Send the goal
        self.navigator.goToPose(goal_pose)
        
        # Monitor the navigation
        start_time = time.time()
        last_feedback_time = time.time()
        
        while not self.navigator.isTaskComplete():
            feedback = self.navigator.getFeedback()
            current_time = time.time()
            
            if feedback and (current_time - last_feedback_time) > 2.0:  # Log every 2 seconds
                self.get_logger().info(f'Distance remaining: {feedback.distance_remaining:.2f}m')
                last_feedback_time = current_time
            
            # Check for timeout
            if current_time - start_time > timeout:
                self.get_logger().error('Navigation timed out!')
                self.navigator.cancelTask()
                return False
            
            time.sleep(0.1)
        
        # Check the result
        result = self.navigator.getResult()
        if result == BasicNavigator.TaskResult.SUCCEEDED:
            self.get_logger().info('✅ Navigation succeeded!')
            return True
        elif result == BasicNavigator.TaskResult.CANCELED:
            self.get_logger().warn('⚠️  Navigation was canceled')
            return False
        elif result == BasicNavigator.TaskResult.FAILED:
            self.get_logger().error('❌ Navigation failed!')
            return False
        else:
            self.get_logger().error(f'❌ Navigation completed with unknown result: {result}')
            return False

    def test_speed_and_precision(self):
        """Test navigation speed and precision with multiple waypoints"""
        self.get_logger().info('🚀 Testing speed and precision...')
        
        # Define test waypoints (x, y, yaw)
        waypoints = [
            (2.0, 0.0, 0.0),      # Forward
            (2.0, 2.0, math.pi/2), # Right turn
            (0.0, 2.0, math.pi),   # Backward
            (0.0, 0.0, -math.pi/2), # Left turn back to start
        ]
        
        total_start_time = time.time()
        successful_goals = 0
        
        for i, (x, y, yaw) in enumerate(waypoints):
            self.get_logger().info(f'📍 Waypoint {i+1}/{len(waypoints)}')
            goal_pose = self.create_goal_pose(x, y, yaw)
            
            start_time = time.time()
            success = self.navigate_to_goal(goal_pose, timeout=45.0)
            end_time = time.time()
            
            if success:
                successful_goals += 1
                self.get_logger().info(f'⏱️  Goal {i+1} completed in {end_time - start_time:.2f} seconds')
            else:
                self.get_logger().error(f'❌ Goal {i+1} failed')
            
            # Brief pause between goals
            time.sleep(1.0)
        
        total_time = time.time() - total_start_time
        success_rate = (successful_goals / len(waypoints)) * 100
        
        self.get_logger().info(f'📊 Test Results:')
        self.get_logger().info(f'   Success Rate: {success_rate:.1f}% ({successful_goals}/{len(waypoints)})')
        self.get_logger().info(f'   Total Time: {total_time:.2f} seconds')
        self.get_logger().info(f'   Average Time per Goal: {total_time/len(waypoints):.2f} seconds')
        
        return success_rate >= 75.0  # Consider 75% success rate as good

    def test_obstacle_avoidance(self):
        """Test dynamic obstacle avoidance by moving in a pattern"""
        self.get_logger().info('🚧 Testing obstacle avoidance...')
        
        # Create a more complex path that would require obstacle avoidance
        complex_waypoints = [
            (1.5, 1.5, 0.0),
            (-1.5, 1.5, math.pi),
            (-1.5, -1.5, -math.pi/2),
            (1.5, -1.5, 0.0),
            (0.0, 0.0, 0.0),  # Return to center
        ]
        
        successful_goals = 0
        for i, (x, y, yaw) in enumerate(complex_waypoints):
            self.get_logger().info(f'🎯 Complex waypoint {i+1}/{len(complex_waypoints)}')
            goal_pose = self.create_goal_pose(x, y, yaw)
            
            if self.navigate_to_goal(goal_pose, timeout=60.0):
                successful_goals += 1
        
        success_rate = (successful_goals / len(complex_waypoints)) * 100
        self.get_logger().info(f'🚧 Obstacle avoidance test: {success_rate:.1f}% success rate')
        
        return success_rate >= 60.0  # Lower threshold for complex navigation

    def run_navigation_tests(self):
        """Run comprehensive navigation tests"""
        self.get_logger().info('🧪 Starting Enhanced Navigation Tests...')
        
        # Test 1: Speed and Precision
        speed_test_passed = self.test_speed_and_precision()
        
        # Test 2: Obstacle Avoidance
        obstacle_test_passed = self.test_obstacle_avoidance()
        
        # Final Results
        self.get_logger().info('📋 Final Test Results:')
        self.get_logger().info(f'   Speed & Precision Test: {"✅ PASSED" if speed_test_passed else "❌ FAILED"}')
        self.get_logger().info(f'   Obstacle Avoidance Test: {"✅ PASSED" if obstacle_test_passed else "❌ FAILED"}')
        
        if speed_test_passed and obstacle_test_passed:
            self.get_logger().info('🎉 All tests PASSED! Navigation system is working excellently!')
        elif speed_test_passed or obstacle_test_passed:
            self.get_logger().info('⚠️  Some tests passed. Navigation system is functional but may need tuning.')
        else:
            self.get_logger().info('❌ Tests failed. Navigation system needs attention.')


def main(args=None):
    rclpy.init(args=args)
    
    try:
        tester = EnhancedNavigationTester()
        rclpy.spin_once(tester, timeout_sec=1.0)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Error: {e}")
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()
