#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped
from nav2_simple_commander.robot_navigator import BasicNavigator
import time


class NavigationTester(Node):
    def __init__(self):
        super().__init__('navigation_tester')
        
        # Initialize the navigator
        self.navigator = BasicNavigator()
        
        # Wait for navigation to fully activate
        self.get_logger().info('Waiting for navigation to become active...')
        self.navigator.waitUntilNav2Active()
        self.get_logger().info('Navigation is active!')
        
        # Test navigation to a simple goal
        self.test_navigation()

    def test_navigation(self):
        """Test navigation to a simple goal"""
        
        # Create a goal pose
        goal_pose = PoseStamped()
        goal_pose.header.frame_id = 'map'
        goal_pose.header.stamp = self.navigator.get_clock().now().to_msg()
        
        # Set goal position (2 meters forward from origin)
        goal_pose.pose.position.x = 2.0
        goal_pose.pose.position.y = 0.0
        goal_pose.pose.position.z = 0.0
        
        # Set goal orientation (facing forward)
        goal_pose.pose.orientation.x = 0.0
        goal_pose.pose.orientation.y = 0.0
        goal_pose.pose.orientation.z = 0.0
        goal_pose.pose.orientation.w = 1.0
        
        self.get_logger().info(f'Navigating to goal: x={goal_pose.pose.position.x}, y={goal_pose.pose.position.y}')
        
        # Send the goal
        self.navigator.goToPose(goal_pose)
        
        # Monitor the navigation
        start_time = time.time()
        timeout = 60.0  # 60 seconds timeout
        
        while not self.navigator.isTaskComplete():
            feedback = self.navigator.getFeedback()
            if feedback:
                self.get_logger().info(f'Navigation feedback: {feedback.distance_remaining:.2f}m remaining')
            
            # Check for timeout
            if time.time() - start_time > timeout:
                self.get_logger().error('Navigation timed out!')
                self.navigator.cancelTask()
                return False
            
            time.sleep(1.0)
        
        # Check the result
        result = self.navigator.getResult()
        if result == BasicNavigator.TaskResult.SUCCEEDED:
            self.get_logger().info('Navigation succeeded!')
            return True
        elif result == BasicNavigator.TaskResult.CANCELED:
            self.get_logger().warn('Navigation was canceled')
            return False
        elif result == BasicNavigator.TaskResult.FAILED:
            self.get_logger().error('Navigation failed!')
            return False
        else:
            self.get_logger().error(f'Navigation completed with unknown result: {result}')
            return False


def main(args=None):
    rclpy.init(args=args)
    
    try:
        tester = NavigationTester()
        rclpy.spin_once(tester, timeout_sec=1.0)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Error: {e}")
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()
