#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseWithCovarianceStamped
import time


class InitialPosePublisher(Node):
    def __init__(self):
        super().__init__('initial_pose_publisher')
        
        # Create publisher for initial pose
        self.pose_publisher = self.create_publisher(
            PoseWithCovarianceStamped,
            '/initialpose',
            10
        )
        
        # Wait a bit for the system to be ready
        time.sleep(2.0)
        
        # Publish initial pose
        self.publish_initial_pose()
        
        self.get_logger().info('Initial pose published successfully')
        
        # Shutdown after publishing
        rclpy.shutdown()

    def publish_initial_pose(self):
        """Publish an initial pose to initialize AMCL localization"""
        pose_msg = PoseWithCovarianceStamped()
        
        # Set header
        pose_msg.header.stamp = self.get_clock().now().to_msg()
        pose_msg.header.frame_id = 'map'
        
        # Set initial position (center of the map)
        pose_msg.pose.pose.position.x = 0.0
        pose_msg.pose.pose.position.y = 0.0
        pose_msg.pose.pose.position.z = 0.0
        
        # Set initial orientation (facing forward)
        pose_msg.pose.pose.orientation.x = 0.0
        pose_msg.pose.pose.orientation.y = 0.0
        pose_msg.pose.pose.orientation.z = 0.0
        pose_msg.pose.pose.orientation.w = 1.0
        
        # Set covariance (uncertainty in the initial pose)
        # Diagonal covariance matrix: [x, y, z, roll, pitch, yaw]
        pose_msg.pose.covariance = [
            0.25, 0.0, 0.0, 0.0, 0.0, 0.0,  # x variance
            0.0, 0.25, 0.0, 0.0, 0.0, 0.0,  # y variance
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0,   # z variance (not used in 2D)
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0,   # roll variance (not used in 2D)
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0,   # pitch variance (not used in 2D)
            0.0, 0.0, 0.0, 0.0, 0.0, 0.068  # yaw variance
        ]
        
        # Publish the pose multiple times to ensure it's received
        for i in range(5):
            pose_msg.header.stamp = self.get_clock().now().to_msg()
            self.pose_publisher.publish(pose_msg)
            self.get_logger().info(f'Published initial pose (attempt {i+1}/5)')
            time.sleep(0.5)


def main(args=None):
    rclpy.init(args=args)
    
    try:
        initial_pose_publisher = InitialPosePublisher()
    except KeyboardInterrupt:
        pass
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()
