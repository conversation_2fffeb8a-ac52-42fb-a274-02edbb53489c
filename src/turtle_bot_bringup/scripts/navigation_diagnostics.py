#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import OccupancyGrid
from sensor_msgs.msg import LaserScan
from tf2_ros import Buffer, TransformListener
import time


class NavigationDiagnostics(Node):
    def __init__(self):
        super().__init__('navigation_diagnostics')
        
        # TF2 setup
        self.tf_buffer = Buffer()
        self.tf_listener = TransformListener(self.tf_buffer, self)
        
        # Subscribers for diagnostics
        self.map_sub = self.create_subscription(OccupancyGrid, '/map', self.map_callback, 10)
        self.scan_sub = self.create_subscription(LaserScan, '/scan', self.scan_callback, 10)
        
        # State variables
        self.map_received = False
        self.scan_received = False
        self.map_info = None
        self.scan_info = None
        
        # Run diagnostics
        self.timer = self.create_timer(2.0, self.run_diagnostics)
        
        self.get_logger().info('🔍 Navigation Diagnostics Started')

    def map_callback(self, msg):
        """Monitor map updates"""
        self.map_received = True
        known_cells = sum(1 for cell in msg.data if cell != -1)
        self.map_info = {
            'width': msg.info.width,
            'height': msg.info.height,
            'resolution': msg.info.resolution,
            'known_cells': known_cells,
            'total_cells': len(msg.data)
        }

    def scan_callback(self, msg):
        """Monitor laser scan"""
        self.scan_received = True
        valid_ranges = [r for r in msg.ranges if msg.range_min <= r <= msg.range_max]
        self.scan_info = {
            'total_points': len(msg.ranges),
            'valid_points': len(valid_ranges),
            'range_min': msg.range_min,
            'range_max': msg.range_max
        }

    def check_transforms(self):
        """Check transform tree connectivity"""
        transforms_to_check = [
            ('map', 'odom'),
            ('odom', 'base_link'),
            ('base_link', 'base_footprint'),
            ('base_link', 'lidar_link')
        ]
        
        transform_status = {}
        
        for parent, child in transforms_to_check:
            try:
                transform = self.tf_buffer.lookup_transform(
                    parent, child, rclpy.time.Time(), timeout=rclpy.duration.Duration(seconds=1.0)
                )
                transform_status[f'{parent}->{child}'] = '✅ Available'
            except Exception as e:
                transform_status[f'{parent}->{child}'] = f'❌ Failed: {str(e)[:50]}'
        
        return transform_status

    def check_navigation_nodes(self):
        """Check if navigation action servers are available"""
        # This is a simplified check - in a real implementation you'd use action client
        node_names = self.get_node_names()
        
        required_nodes = [
            'controller_server',
            'planner_server',
            'bt_navigator',
            'slam_toolbox'
        ]
        
        node_status = {}
        for node in required_nodes:
            if any(node in name for name in node_names):
                node_status[node] = '✅ Running'
            else:
                node_status[node] = '❌ Not found'
        
        return node_status

    def run_diagnostics(self):
        """Run comprehensive navigation diagnostics"""
        self.get_logger().info('🔍 Running Navigation Diagnostics...')
        
        # Check transforms
        self.get_logger().info('📡 Transform Tree Status:')
        transform_status = self.check_transforms()
        for transform, status in transform_status.items():
            self.get_logger().info(f'   {transform}: {status}')
        
        # Check nodes
        self.get_logger().info('🤖 Navigation Nodes Status:')
        node_status = self.check_navigation_nodes()
        for node, status in node_status.items():
            self.get_logger().info(f'   {node}: {status}')
        
        # Check map
        if self.map_received and self.map_info:
            self.get_logger().info('🗺️  Map Status:')
            self.get_logger().info(f'   Size: {self.map_info["width"]}x{self.map_info["height"]} cells')
            self.get_logger().info(f'   Resolution: {self.map_info["resolution"]:.3f} m/cell')
            self.get_logger().info(f'   Known area: {self.map_info["known_cells"]}/{self.map_info["total_cells"]} cells')
            
            if self.map_info["known_cells"] < 100:
                self.get_logger().warn('⚠️  Map has very few known cells. Robot may need to move for SLAM to work.')
        else:
            self.get_logger().warn('❌ No map received from SLAM')
        
        # Check laser scan
        if self.scan_received and self.scan_info:
            self.get_logger().info('📡 Laser Scan Status:')
            self.get_logger().info(f'   Valid points: {self.scan_info["valid_points"]}/{self.scan_info["total_points"]}')
            self.get_logger().info(f'   Range: {self.scan_info["range_min"]:.2f} - {self.scan_info["range_max"]:.2f} m')
        else:
            self.get_logger().warn('❌ No laser scan received')
        
        # Overall assessment
        all_transforms_ok = all('✅' in status for status in transform_status.values())
        all_nodes_ok = all('✅' in status for status in node_status.values())
        map_ok = self.map_received and self.map_info and self.map_info["known_cells"] > 50
        scan_ok = self.scan_received
        
        if all_transforms_ok and all_nodes_ok and map_ok and scan_ok:
            self.get_logger().info('🎉 All systems operational! Navigation should work with RViz goals.')
        else:
            self.get_logger().warn('⚠️  Some issues detected. Check the status above.')
            
            if not map_ok:
                self.get_logger().info('💡 Tip: Try moving the robot manually or run slam_initialization.py')
            
            if not all_transforms_ok:
                self.get_logger().info('💡 Tip: Check if all navigation nodes are running properly')
        
        self.get_logger().info('─' * 60)


def main(args=None):
    rclpy.init(args=args)
    
    try:
        diagnostics = NavigationDiagnostics()
        rclpy.spin(diagnostics)
    except KeyboardInterrupt:
        pass
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()
