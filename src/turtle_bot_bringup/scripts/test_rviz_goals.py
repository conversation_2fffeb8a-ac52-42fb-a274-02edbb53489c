#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped
from nav2_simple_commander.robot_navigator import BasicNavigator
import time
import math


class RVizGoalTester(Node):
    def __init__(self):
        super().__init__('rviz_goal_tester')
        
        # Initialize the navigator
        self.navigator = BasicNavigator()
        
        # Wait for navigation to fully activate
        self.get_logger().info('🚀 Waiting for navigation to become active...')
        self.navigator.waitUntilNav2Active()
        self.get_logger().info('✅ Navigation is active!')
        
        # Test RViz-style goals
        self.test_rviz_goals()

    def create_goal_pose(self, x, y, yaw=0.0):
        """Create a goal pose similar to RViz 2D Goal Pose"""
        goal_pose = PoseStamped()
        goal_pose.header.frame_id = 'map'
        goal_pose.header.stamp = self.navigator.get_clock().now().to_msg()
        
        goal_pose.pose.position.x = x
        goal_pose.pose.position.y = y
        goal_pose.pose.position.z = 0.0
        
        # Convert yaw to quaternion
        goal_pose.pose.orientation.x = 0.0
        goal_pose.pose.orientation.y = 0.0
        goal_pose.pose.orientation.z = math.sin(yaw / 2.0)
        goal_pose.pose.orientation.w = math.cos(yaw / 2.0)
        
        return goal_pose

    def test_single_goal(self, x, y, yaw=0.0, timeout=30.0):
        """Test navigation to a single goal"""
        goal_pose = self.create_goal_pose(x, y, yaw)
        
        self.get_logger().info(f'🎯 Testing goal: x={x:.2f}, y={y:.2f}, yaw={yaw:.2f}')
        
        # Send the goal
        self.navigator.goToPose(goal_pose)
        
        # Monitor the navigation
        start_time = time.time()
        
        while not self.navigator.isTaskComplete():
            feedback = self.navigator.getFeedback()
            if feedback:
                self.get_logger().info(f'📍 Distance remaining: {feedback.distance_remaining:.2f}m')
            
            # Check for timeout
            if time.time() - start_time > timeout:
                self.get_logger().error('⏰ Navigation timed out!')
                self.navigator.cancelTask()
                return False
            
            time.sleep(1.0)
        
        # Check the result
        result = self.navigator.getResult()
        if result == BasicNavigator.TaskResult.SUCCEEDED:
            self.get_logger().info('✅ Goal reached successfully!')
            return True
        elif result == BasicNavigator.TaskResult.CANCELED:
            self.get_logger().warn('⚠️  Goal was canceled')
            return False
        elif result == BasicNavigator.TaskResult.FAILED:
            self.get_logger().error('❌ Goal failed!')
            return False
        else:
            self.get_logger().error(f'❌ Unknown result: {result}')
            return False

    def test_rviz_goals(self):
        """Test goals that simulate RViz 2D Goal Pose behavior"""
        self.get_logger().info('🧪 Testing RViz-style navigation goals...')
        
        # Test goals with increasing distance and complexity
        test_goals = [
            (0.5, 0.0, 0.0),      # Very close goal
            (1.0, 0.0, 0.0),      # Close goal forward
            (1.0, 0.5, 0.0),      # Diagonal goal
            (0.5, 0.5, math.pi/4), # Goal with rotation
            (0.0, 0.0, 0.0),      # Return to origin
        ]
        
        successful_goals = 0
        
        for i, (x, y, yaw) in enumerate(test_goals):
            self.get_logger().info(f'📋 Test {i+1}/{len(test_goals)}')
            
            if self.test_single_goal(x, y, yaw, timeout=45.0):
                successful_goals += 1
                self.get_logger().info(f'✅ Test {i+1} PASSED')
            else:
                self.get_logger().error(f'❌ Test {i+1} FAILED')
            
            # Brief pause between goals
            time.sleep(2.0)
        
        # Final results
        success_rate = (successful_goals / len(test_goals)) * 100
        self.get_logger().info('📊 Final Results:')
        self.get_logger().info(f'   Success Rate: {success_rate:.1f}% ({successful_goals}/{len(test_goals)})')
        
        if success_rate >= 80:
            self.get_logger().info('🎉 RViz goal navigation is working well!')
        elif success_rate >= 50:
            self.get_logger().info('⚠️  RViz goal navigation is partially working. May need tuning.')
        else:
            self.get_logger().error('❌ RViz goal navigation has significant issues.')
        
        # Provide guidance
        self.get_logger().info('💡 To use RViz goals:')
        self.get_logger().info('   1. Open RViz2')
        self.get_logger().info('   2. Add "2D Goal Pose" tool to toolbar')
        self.get_logger().info('   3. Click and drag on map to set goal')
        self.get_logger().info('   4. Robot should navigate to the goal automatically')


def main(args=None):
    rclpy.init(args=args)
    
    try:
        tester = RVizGoalTester()
        rclpy.spin_once(tester, timeout_sec=1.0)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Error: {e}")
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()
