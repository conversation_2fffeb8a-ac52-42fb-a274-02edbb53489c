#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist, PoseWithCovarianceStamped
from nav_msgs.msg import OccupancyGrid
import time
import math


class SLAMInitializer(Node):
    def __init__(self):
        super().__init__('slam_initializer')
        
        # Publishers
        self.cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        self.initial_pose_pub = self.create_publisher(PoseWithCovarianceStamped, '/initialpose', 10)
        
        # Subscribers
        self.map_sub = self.create_subscription(OccupancyGrid, '/map', self.map_callback, 10)
        
        # State variables
        self.map_received = False
        self.map_size = 0
        self.initialization_complete = False
        
        self.get_logger().info('SLAM Initializer started. Waiting for map...')
        
        # Start initialization sequence
        self.timer = self.create_timer(1.0, self.initialization_sequence)

    def map_callback(self, msg):
        """Callback for map updates"""
        self.map_received = True
        # Count non-unknown cells (mapped area)
        known_cells = sum(1 for cell in msg.data if cell != -1)
        self.map_size = known_cells
        
        if not self.initialization_complete and known_cells > 100:  # Threshold for sufficient mapping
            self.get_logger().info(f'Map has {known_cells} known cells. SLAM initialization complete!')
            self.initialization_complete = True

    def publish_initial_pose(self):
        """Publish initial pose for SLAM"""
        initial_pose = PoseWithCovarianceStamped()
        initial_pose.header.frame_id = 'map'
        initial_pose.header.stamp = self.get_clock().now().to_msg()
        
        # Set pose at origin
        initial_pose.pose.pose.position.x = 0.0
        initial_pose.pose.pose.position.y = 0.0
        initial_pose.pose.pose.position.z = 0.0
        initial_pose.pose.pose.orientation.w = 1.0
        
        # Set covariance (uncertainty)
        initial_pose.pose.covariance = [0.1] * 36  # Low uncertainty
        
        self.initial_pose_pub.publish(initial_pose)
        self.get_logger().info('Published initial pose for SLAM')

    def move_robot_for_mapping(self, duration=5.0):
        """Move robot in a pattern to help SLAM build initial map"""
        self.get_logger().info('Moving robot to help SLAM build initial map...')
        
        # Rotation movement to scan environment
        twist = Twist()
        twist.angular.z = 0.5  # Rotate slowly
        
        start_time = time.time()
        rate = self.create_rate(10)  # 10 Hz
        
        while (time.time() - start_time) < duration:
            self.cmd_vel_pub.publish(twist)
            time.sleep(0.1)
        
        # Stop rotation
        twist.angular.z = 0.0
        self.cmd_vel_pub.publish(twist)
        
        self.get_logger().info('Initial mapping movement complete')

    def initialization_sequence(self):
        """Main initialization sequence"""
        if self.initialization_complete:
            self.timer.cancel()
            self.get_logger().info('🎉 SLAM initialization complete! Ready for navigation goals.')
            return
        
        if not self.map_received:
            self.get_logger().info('Waiting for SLAM map...')
            return
        
        if self.map_size < 50:  # Not enough map built yet
            self.get_logger().info(f'Map size: {self.map_size} cells. Helping SLAM build map...')
            self.move_robot_for_mapping(3.0)  # Short movement
        else:
            self.get_logger().info(f'Map size: {self.map_size} cells. SLAM ready!')
            self.initialization_complete = True


def main(args=None):
    rclpy.init(args=args)
    
    try:
        initializer = SLAMInitializer()
        rclpy.spin(initializer)
    except KeyboardInterrupt:
        pass
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()
