cmake_minimum_required(VERSION 3.8)
project(turtle_bot_bringup)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)

# Install directories
install(
  DIRECTORY launch scripts
  DESTINATION share/${PROJECT_NAME}
  USE_SOURCE_PERMISSIONS
)

# Install Python scripts as executables
install(PROGRAMS
  scripts/wasd_controller.py
  scripts/initial_pose_publisher.py
  scripts/launch_rviz.py
  scripts/test_navigation.py
  scripts/test_navigation_enhanced.py
  DESTINATION lib/${PROJECT_NAME}
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
