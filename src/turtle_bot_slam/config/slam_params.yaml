# Optimized SLAM Parameters for Smooth Navigation and Real-time Performance
slam_toolbox:
  ros__parameters:

    # Plugin params - Optimized for performance
    solver_plugin: solver_plugins::CeresSolver
    ceres_linear_solver: SPARSE_NORMAL_CHOLESKY
    ceres_preconditioner: SCHUR_JACOBI
    ceres_trust_strategy: LEVENBERG_MARQUARDT
    ceres_dogleg_type: TRADITIONAL_DOGLEG
    ceres_loss_function: None

    # ROS Parameters
    odom_frame: odom
    map_frame: map
    base_frame: base_footprint
    scan_topic: /scan
    mode: mapping #localization

    # if you'd like to immediately start continuing a map at a given pose
    # or at the dock, but they are mutually exclusive, if pose is given
    # will use pose
    #map_file_name: test_steve
    #map_start_pose: [0.0, 0.0, 0.0]
    #map_start_at_dock: true

    debug_logging: false
    throttle_scans: 1
    transform_publish_period: 0.02 #if 0 never publishes odometry
    map_update_interval: 1.5  # Even faster map updates for improved lidar
    resolution: 0.05
    max_laser_range: 15.0  # Match improved lidar range
    minimum_time_interval: 0.25  # More responsive with better lidar data
    transform_timeout: 0.2
    tf_buffer_duration: 30.
    stack_size_to_use: 40000000 #// program needs a larger stack size to serialize large maps
    enable_interactive_mode: true

    # General Parameters - Optimized for improved lidar performance
    use_scan_matching: true
    use_scan_barycenter: true
    minimum_travel_distance: 0.25  # More frequent updates with better lidar data
    minimum_travel_heading: 0.25   # More responsive with higher resolution
    scan_buffer_size: 20           # Larger buffer for 720-sample lidar
    scan_buffer_maximum_scan_distance: 15.0  # Match improved lidar range
    link_match_minimum_response_fine: 0.12  # Better scan matching with more samples
    link_scan_maximum_distance: 2.5        # Increased for better loop closure
    loop_search_maximum_distance: 5.0      # Better loop closure with improved range
    do_loop_closing: true
    loop_match_minimum_chain_size: 6       # More aggressive with better data
    loop_match_maximum_variance_coarse: 2.0
    loop_match_minimum_response_coarse: 0.45
    loop_match_minimum_response_fine: 0.55      # Higher confidence with better data

    # Correlation Parameters - Correlation Parameters
    correlation_search_space_dimension: 0.5
    correlation_search_space_resolution: 0.01
    correlation_search_space_smear_deviation: 0.1 

    # Correlation Parameters - Loop Closure Parameters
    loop_search_space_dimension: 8.0
    loop_search_space_resolution: 0.05
    loop_search_space_smear_deviation: 0.03

    # Scan Matcher Parameters
    distance_variance_penalty: 0.5      
    angle_variance_penalty: 1.0    

    fine_search_angle_offset: 0.00349     
    coarse_search_angle_offset: 0.349   
    coarse_angle_resolution: 0.0349        
    minimum_angle_penalty: 0.9
    minimum_distance_penalty: 0.5
    use_response_expansion: true
