# SLAM Toolbox Configuration for Real-time Mapping
# Optimized for fast and accurate mapping while navigating

slam_toolbox:
  ros__parameters:
    # ROS Parameters
    use_sim_time: True
    
    # General Parameters
    solver_plugin: solver_plugins::CeresSolver
    ceres_linear_solver: SPARSE_NORMAL_CHOLESKY
    ceres_preconditioner: SCHUR_JACOBI
    ceres_trust_strategy: LEVENBERG_MARQUARDT
    ceres_dogleg_type: TRADITIONAL_DOGLEG
    ceres_loss_function: None
    
    # ROS Parameters
    odom_frame: odom
    map_frame: map
    base_frame: base_link
    scan_topic: /scan
    mode: mapping  # mapping, localization
    
    # if you'd like to immediately start continuing a map at a given pose
    # or at the dock, but they are mutually exclusive, if pose is given
    # will use pose
    #map_file_name: test_steve
    #map_start_pose: [0.0, 0.0, 0.0]
    #map_start_at_dock: true
    
    debug_logging: false
    throttle_scans: 1
    transform_publish_period: 0.02  # Increased frequency for better precision
    map_update_interval: 2.0        # Faster map updates
    resolution: 0.03                # Higher resolution for better precision
    max_laser_range: 12.0           # Increased range
    minimum_time_interval: 0.5
    transform_timeout: 0.2
    tf_buffer_duration: 30.
    stack_size_to_use: 40000000     # Increased stack size
    enable_interactive_mode: true
    
    # General Parameters
    use_scan_matching: true
    use_scan_barycenter: true
    minimum_travel_distance: 0.3    # Reduced for more frequent updates
    minimum_travel_heading: 0.3     # Reduced for more frequent updates
    scan_buffer_size: 10
    scan_buffer_maximum_scan_distance: 10.0
    link_match_minimum_response_fine: 0.1
    link_scan_maximum_distance: 1.5
    loop_search_maximum_distance: 3.0
    do_loop_closing: true
    loop_match_minimum_chain_size: 10
    loop_match_maximum_variance_coarse: 3.0
    loop_match_minimum_response_coarse: 0.35
    loop_match_minimum_response_fine: 0.45
    
    # Correlation Parameters - Tuned for speed and accuracy
    correlation_search_space_dimension: 0.5
    correlation_search_space_resolution: 0.01
    correlation_search_space_smear_deviation: 0.1
    
    # Correlation Parameters - Loop Closure
    loop_search_space_dimension: 8.0
    loop_search_space_resolution: 0.05
    loop_search_space_smear_deviation: 0.03
    
    # Scan Matcher Parameters
    distance_variance_penalty: 0.5
    angle_variance_penalty: 1.0
    
    fine_search_angle_offset: 0.00349  # 0.2 degrees
    coarse_search_angle_offset: 0.349  # 20 degrees
    coarse_angle_resolution: 0.0349    # 2 degrees
    minimum_angle_penalty: 0.9
    minimum_distance_penalty: 0.5
    use_response_expansion: true
