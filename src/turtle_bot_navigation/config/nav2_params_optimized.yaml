# Optimized Navigation Parameters for Fast and Precise Movement
# Enhanced for better speed, precision, and responsiveness

amcl:
  ros__parameters:
    use_sim_time: True
    alpha1: 0.1  # Reduced for better precision
    alpha2: 0.1
    alpha3: 0.1
    alpha4: 0.1
    alpha5: 0.1
    base_frame_id: "base_link"
    beam_skip_distance: 0.5
    beam_skip_error_threshold: 0.9
    beam_skip_threshold: 0.3
    do_beamskip: false
    global_frame_id: "map"
    lambda_short: 0.1
    laser_likelihood_max_dist: 2.0
    laser_max_range: 100.0
    laser_min_range: -1.0
    laser_model_type: "likelihood_field"
    max_beams: 60
    max_particles: 1000  # Reduced for better performance
    min_particles: 200   # Reduced for better performance
    odom_frame_id: "odom"
    pf_err: 0.03         # Improved precision
    pf_z: 0.99
    recovery_alpha_fast: 0.0
    recovery_alpha_slow: 0.0
    resample_interval: 1
    robot_model_type: "nav2_amcl::DifferentialMotionModel"
    save_pose_rate: 0.5
    sigma_hit: 0.15      # Improved precision
    tf_broadcast: true
    transform_tolerance: 0.5  # Reduced for better precision
    update_min_a: 0.1    # More frequent updates
    update_min_d: 0.1    # More frequent updates
    z_hit: 0.5
    z_max: 0.05
    z_rand: 0.5
    z_short: 0.05
    scan_topic: scan

bt_navigator:
  ros__parameters:
    use_sim_time: True
    global_frame: map
    robot_base_frame: base_link
    odom_topic: /odom
    bt_loop_duration: 10
    default_server_timeout: 20
    navigators: ["navigate_to_pose", "navigate_through_poses"]
    navigate_to_pose:
      plugin: "nav2_bt_navigator/NavigateToPoseNavigator"
    navigate_through_poses:
      plugin: "nav2_bt_navigator/NavigateThroughPosesNavigator"
    plugin_lib_names:
    - nav2_compute_path_to_pose_action_bt_node
    - nav2_compute_path_through_poses_action_bt_node
    - nav2_smooth_path_action_bt_node
    - nav2_follow_path_action_bt_node
    - nav2_spin_action_bt_node
    - nav2_wait_action_bt_node
    - nav2_assisted_teleop_action_bt_node
    - nav2_back_up_action_bt_node
    - nav2_drive_on_heading_bt_node
    - nav2_clear_costmap_service_bt_node
    - nav2_is_stuck_condition_bt_node
    - nav2_goal_reached_condition_bt_node
    - nav2_goal_updated_condition_bt_node
    - nav2_globally_updated_goal_condition_bt_node
    - nav2_is_path_valid_condition_bt_node
    - nav2_initial_pose_received_condition_bt_node
    - nav2_reinitialize_global_localization_service_bt_node
    - nav2_rate_controller_bt_node
    - nav2_distance_controller_bt_node
    - nav2_speed_controller_bt_node
    - nav2_truncate_path_action_bt_node
    - nav2_truncate_path_local_action_bt_node
    - nav2_goal_updater_node_bt_node
    - nav2_recovery_node_bt_node
    - nav2_pipeline_sequence_bt_node
    - nav2_round_robin_node_bt_node
    - nav2_transform_available_condition_bt_node
    - nav2_time_expired_condition_bt_node
    - nav2_path_expiring_timer_condition
    - nav2_distance_traveled_condition_bt_node
    - nav2_single_trigger_bt_node
    - nav2_goal_updated_controller_bt_node
    - nav2_is_battery_low_condition_bt_node
    - nav2_navigate_through_poses_action_bt_node
    - nav2_navigate_to_pose_action_bt_node
    - nav2_remove_passed_goals_action_bt_node
    - nav2_planner_selector_bt_node
    - nav2_controller_selector_bt_node
    - nav2_goal_checker_selector_bt_node
    - nav2_controller_cancel_bt_node
    - nav2_path_longer_on_approach_bt_node
    - nav2_wait_cancel_bt_node
    - nav2_spin_cancel_bt_node
    - nav2_back_up_cancel_bt_node
    - nav2_assisted_teleop_cancel_bt_node
    - nav2_drive_on_heading_cancel_bt_node
    - nav2_is_battery_charging_condition_bt_node

controller_server:
  ros__parameters:
    use_sim_time: True
    controller_frequency: 100.0  # Increased for better responsiveness
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.5
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["general_goal_checker"]
    controller_plugins: ["FollowPath"]

    # Progress checker parameters - More aggressive
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.3  # Reduced for faster detection
      movement_time_allowance: 5.0   # Reduced timeout
    
    # Goal checker parameters - More precise
    general_goal_checker:
      stateful: True
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.1   # More precise
      yaw_goal_tolerance: 0.1  # More precise
    
    # DWB parameters - Optimized for speed and precision
    FollowPath:
      plugin: "dwb_core::DWBLocalPlanner"
      debug_trajectory_details: False
      min_vel_x: 0.0
      min_vel_y: 0.0
      max_vel_x: 1.2        # Increased max speed
      max_vel_y: 0.0
      max_vel_theta: 2.0    # Increased angular speed
      min_speed_xy: 0.0
      max_speed_xy: 1.2     # Increased max speed
      min_speed_theta: 0.0
      acc_lim_x: 2.5        # Increased acceleration
      acc_lim_y: 0.0
      acc_lim_theta: 3.0    # Increased angular acceleration
      decel_lim_x: -2.5     # Increased deceleration
      decel_lim_y: 0.0
      decel_lim_theta: -3.0 # Increased angular deceleration
      vx_samples: 50        # More samples for better precision
      vy_samples: 1
      vtheta_samples: 50    # More samples for better precision
      sim_time: 2.0         # Reduced for faster planning
      linear_granularity: 0.005  # Finer granularity
      angular_granularity: 0.0025  # Finer granularity
      transform_tolerance: 0.1     # Reduced for better precision
      xy_goal_tolerance: 0.1       # More precise
      trans_stopped_velocity: 0.02 # Lower threshold
      short_circuit_trajectory_evaluation: True  # Enable for speed
      stateful: True
      trajectory_generator_name: dwb_plugins::StandardTrajectoryGenerator
      include_last_point: True
      critics: ["RotateToGoal", "Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist"]
      BaseObstacle.scale: 0.01     # Reduced for smoother movement
      PathAlign.scale: 20.0        # Increased for better path following
      PathAlign.forward_point_distance: 0.15
      GoalAlign.scale: 15.0        # Increased for better goal alignment
      GoalAlign.forward_point_distance: 0.15
      PathDist.scale: 20.0         # Increased for better path following
      GoalDist.scale: 15.0         # Increased for better goal approach
      RotateToGoal.scale: 10.0     # Increased for better rotation
      RotateToGoal.slowing_factor: 3.0  # Increased for smoother approach
      RotateToGoal.lookahead_time: -1.0

local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 20.0      # Increased for better responsiveness
      publish_frequency: 10.0     # Increased for better responsiveness
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: True
      rolling_window: true
      width: 4                    # Increased for better planning
      height: 4                   # Increased for better planning
      resolution: 0.03            # Finer resolution for precision
      robot_radius: 0.22
      plugins: ["static_layer", "voxel_layer", "inflation_layer"]
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 2.0   # Increased for better obstacle avoidance
        inflation_radius: 0.25     # Reduced for more precise navigation
      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: True
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 2.0
        mark_threshold: 0
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 4.0   # Increased range
          raytrace_min_range: 0.0
          obstacle_max_range: 3.0   # Increased range
          obstacle_min_range: 0.0
      always_send_full_costmap: True

global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 5.0       # Increased for better responsiveness
      publish_frequency: 2.0      # Increased for better responsiveness
      global_frame: map
      robot_base_frame: base_link
      use_sim_time: True
      robot_radius: 0.22
      resolution: 0.05
      track_unknown_space: true
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 4.0   # Increased range
          raytrace_min_range: 0.0
          obstacle_max_range: 3.0   # Increased range
          obstacle_min_range: 0.0
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 2.0   # Increased for better obstacle avoidance
        inflation_radius: 0.25     # Reduced for more precise navigation
      always_send_full_costmap: True

map_server:
  ros__parameters:
    use_sim_time: True
    yaml_filename: ""

map_saver:
  ros__parameters:
    use_sim_time: True
    save_map_timeout: 5.0
    free_thresh_default: 0.25
    occupied_thresh_default: 0.65
    map_subscribe_transient_local: True

planner_server:
  ros__parameters:
    expected_planner_frequency: 50.0  # Increased for faster planning
    use_sim_time: True
    planner_plugins: ["GridBased"]
    GridBased:
      plugin: "nav2_navfn_planner/NavfnPlanner"
      tolerance: 0.2              # Reduced for more precise planning
      use_astar: true             # Enable A* for better paths
      allow_unknown: true

smoother_server:
  ros__parameters:
    use_sim_time: True
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-8           # Increased precision
      max_its: 5000               # More iterations for better smoothing
      do_refinement: True
      refinement_num: 5           # More refinement passes

behavior_server:
  ros__parameters:
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 20.0         # Increased for better responsiveness
    behavior_plugins: ["spin", "backup", "drive_on_heading", "assisted_teleop", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    assisted_teleop:
      plugin: "nav2_behaviors/AssistedTeleop"
    global_frame: odom
    robot_base_frame: base_link
    transform_tolerance: 0.05     # Reduced for better precision
    use_sim_time: true
    simulate_ahead_time: 1.5      # Reduced for faster response
    max_rotational_vel: 2.0       # Increased for faster rotation
    min_rotational_vel: 0.2       # Reduced minimum
    rotational_acc_lim: 4.0       # Increased acceleration

robot_state_publisher:
  ros__parameters:
    use_sim_time: True

waypoint_follower:
  ros__parameters:
    use_sim_time: True
    loop_rate: 50                 # Increased for better responsiveness
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 100  # Reduced pause time

velocity_smoother:
  ros__parameters:
    use_sim_time: True
    smoothing_frequency: 100.0    # Increased for smoother motion
    scale_velocities: True
    feedback: "CLOSED_LOOP"
    max_velocity: [1.2, 0.0, 2.0]  # Increased max velocities
    min_velocity: [-1.2, 0.0, -2.0]  # Increased max velocities
    max_accel: [2.5, 0.0, 3.0]    # Increased accelerations
    max_decel: [-2.5, 0.0, -3.0]  # Increased decelerations
    odom_topic: "odom"
    odom_duration: 0.05           # Reduced for better responsiveness
    deadband_velocity: [0.01, 0.0, 0.02]  # Reduced deadband
    velocity_timeout: 1.0
    cmd_vel_timeout: 0.1          # Reduced timeout
