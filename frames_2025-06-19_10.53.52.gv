digraph G {
"base_footprint" -> "drivewhl_r_link"[label=" Broadcaster: default_authority\nAverage rate: 83.832\nBuffer length: 2.004\nMost recent transform: 44.404\nOldest transform: 42.4\n"];
"base_link" -> "base_footprint"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"odom" -> "base_link"[label=" Broadcaster: default_authority\nAverage rate: 83.845\nBuffer length: 1.956\nMost recent transform: 44.392\nOldest transform: 42.436\n"];
"base_footprint" -> "drivewhl_l_link"[label=" Broadcaster: default_authority\nAverage rate: 83.832\nBuffer length: 2.004\nMost recent transform: 44.404\nOldest transform: 42.4\n"];
"map" -> "odom"[label=" Broadcaster: default_authority\nAverage rate: 26.946\nBuffer length: 2.004\nMost recent transform: 44.592\nOldest transform: 42.588\n"];
"base_link" -> "camera_link"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"camera_link" -> "camera_link_optical"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"base_link" -> "front_caster"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"base_link" -> "imu_link"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"base_link" -> "lidar_link"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
"base_link" -> "uv_light_link"[label=" Broadcaster: default_authority\nAverage rate: 10000.0\nBuffer length: 0.0\nMost recent transform: 0.0\nOldest transform: 0.0\n"];
edge [style=invis];
 subgraph cluster_legend { style=bold; color=black; label ="view_frames Result";
"Recorded at time: 1750326832.9907446"[ shape=plaintext ] ;
}->"map";
}