[0.111s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'turtle_bot_bringup']
[0.111s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['turtle_bot_bringup'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7cd5d756b040>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7cd5d756ab00>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7cd5d756ab00>>, mixin_verb=('build',))
[0.252s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.253s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.253s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.253s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.253s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.253s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.253s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/turtle2'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ros']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ros'
[0.268s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_bringup' with type 'ros.ament_cmake' and name 'turtle_bot_bringup'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ignore', 'ignore_ament_install']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore_ament_install'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_pkg']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_pkg'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_meta']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_meta'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ros']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ros'
[0.269s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_description' with type 'ros.ament_cmake' and name 'turtle_bot_description'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ignore', 'ignore_ament_install']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore_ament_install'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_pkg']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_pkg'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_meta']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_meta'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ros']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ros'
[0.270s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_gazebo' with type 'ros.ament_cmake' and name 'turtle_bot_gazebo'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore_ament_install'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_pkg']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_pkg'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_meta']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_meta'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ros']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ros'
[0.271s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_navigation' with type 'ros.ament_cmake' and name 'turtle_bot_navigation'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ignore', 'ignore_ament_install']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore_ament_install'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_pkg']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_pkg'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_meta']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_meta'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ros']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ros'
[0.279s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_slam' with type 'ros.ament_cmake' and name 'turtle_bot_slam'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['ignore', 'ignore_ament_install']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ignore'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ignore_ament_install'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['colcon_pkg']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'colcon_pkg'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['colcon_meta']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'colcon_meta'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['ros']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ros'
[0.281s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_uv_system' with type 'ros.ament_python' and name 'turtle_bot_uv_system'
[0.281s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.281s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.281s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.281s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.281s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.308s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'turtle_bot_description' in 'src/turtle_bot_description'
[0.308s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'turtle_bot_uv_system' in 'src/turtle_bot_uv_system'
[0.308s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'turtle_bot_gazebo' in 'src/turtle_bot_gazebo'
[0.308s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'turtle_bot_navigation' in 'src/turtle_bot_navigation'
[0.308s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'turtle_bot_slam' in 'src/turtle_bot_slam'
[0.309s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.309s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.312s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 430 installed packages in /opt/ros/humble
[0.314s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.351s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_args' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_first' from command line to 'False'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_force_configure' from command line to 'False'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'ament_cmake_args' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.352s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_bringup', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_bringup', 'symlink_install': False, 'test_result_base': None}
[0.352s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.354s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.354s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_bringup' with build type 'ament_cmake'
[0.354s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_bringup'
[0.359s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.360s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.360s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.381s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_bringup -- -j4 -l4
[0.443s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_bringup -- -j4 -l4
[0.455s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_bringup
[0.479s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[0.481s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_bringup
[0.485s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake module files
[0.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake config files
[0.486s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[0.486s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[0.487s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[0.488s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib'
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/python3.10/site-packages'
[0.491s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[0.491s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[0.492s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[0.494s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[0.495s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[0.498s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[0.499s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake module files
[0.500s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake config files
[0.500s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[0.500s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[0.501s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[0.502s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/python3.10/site-packages'
[0.504s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[0.504s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[0.505s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[0.506s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[0.507s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[0.508s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[0.510s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[0.510s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.511s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.511s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.511s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.523s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.523s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.523s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.552s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.552s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.ps1'
[0.553s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle2/install/_local_setup_util_ps1.py'
[0.555s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.ps1'
[0.557s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.sh'
[0.557s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle2/install/_local_setup_util_sh.py'
[0.558s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.sh'
[0.559s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.bash'
[0.560s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.bash'
[0.561s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.zsh'
[0.562s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.zsh'
