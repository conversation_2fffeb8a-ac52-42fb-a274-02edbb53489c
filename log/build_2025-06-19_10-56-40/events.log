[0.000000] (-) TimerEvent: {}
[0.000352] (-) JobUnselected: {'identifier': 'turtle_bot_description'}
[0.001251] (-) JobUnselected: {'identifier': 'turtle_bot_gazebo'}
[0.001388] (-) JobUnselected: {'identifier': 'turtle_bot_navigation'}
[0.001556] (-) JobUnselected: {'identifier': 'turtle_bot_slam'}
[0.001578] (-) JobUnselected: {'identifier': 'turtle_bot_uv_system'}
[0.001602] (turtle_bot_bringup) JobQueued: {'identifier': 'turtle_bot_bringup', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle2/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle2/install/turtle_bot_gazebo'), ('turtle_bot_navigation', '/home/<USER>/turtle2/install/turtle_bot_navigation'), ('turtle_bot_slam', '/home/<USER>/turtle2/install/turtle_bot_slam')])}
[0.001639] (turtle_bot_bringup) JobStarted: {'identifier': 'turtle_bot_bringup'}
[0.022765] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'cmake'}
[0.024142] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'build'}
[0.024277] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle2/build/turtle_bot_bringup', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_bringup', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/snap/code/195/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('TERM_PROGRAM_VERSION', '1.100.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/195/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/195/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/195/usr/share/code/code'), ('MANAGERPID', '1322'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '1492'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3162'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('JOURNAL_STREAM', '8:13321'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('GTK_EXE_PREFIX', '/snap/code/195/usr'), ('INVOCATION_ID', '3b4f4e0c9dac4e95991c510f0ff4d5e0'), ('XDG_MENU_PREFIX', 'gnome-'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/195/usr/lib/locale'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-d3d5ebed93991d45.txt'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/195/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2c1c68f5d9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/snap/code/195/.local/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/195/.local/share:/home/<USER>/snap/code/195:/snap/code/195/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[0.084345] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[0.087543] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'install'}
[0.098142] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle2/build/turtle_bot_bringup'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_bringup', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/snap/code/195/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('TERM_PROGRAM_VERSION', '1.100.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/195/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/195/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/195/usr/share/code/code'), ('MANAGERPID', '1322'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '1492'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3162'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('JOURNAL_STREAM', '8:13321'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/.local/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('GTK_EXE_PREFIX', '/snap/code/195/usr'), ('INVOCATION_ID', '3b4f4e0c9dac4e95991c510f0ff4d5e0'), ('XDG_MENU_PREFIX', 'gnome-'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/195/usr/lib/locale'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-d3d5ebed93991d45.txt'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/195/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2c1c68f5d9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/snap/code/195/.local/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/195/.local/share:/home/<USER>/snap/code/195:/snap/code/195/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[0.099125] (-) TimerEvent: {}
[0.107351] (turtle_bot_bringup) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.108309] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch\n'}
[0.108394] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam.launch.py\n'}
[0.108436] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation_with_uv.launch.py\n'}
[0.109357] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation_optimized.launch.py\n'}
[0.109601] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_simulation.launch.py\n'}
[0.109659] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot.rviz\n'}
[0.109817] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam_navigation_fixed.launch.py\n'}
[0.110012] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation.launch.py\n'}
[0.110407] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam_navigation.launch.py\n'}
[0.110558] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts\n'}
[0.110943] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/test_navigation.py\n'}
[0.111018] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/launch_rviz.py\n'}
[0.111201] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/wasd_controller.py\n'}
[0.111615] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/test_navigation_enhanced.py\n'}
[0.111803] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/initial_pose_publisher.py\n'}
[0.111937] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/wasd_controller.py\n'}
[0.112033] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/initial_pose_publisher.py\n'}
[0.115297] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/launch_rviz.py\n'}
[0.116185] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/test_navigation.py\n'}
[0.116908] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/test_navigation_enhanced.py\n'}
[0.118688] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/package_run_dependencies/turtle_bot_bringup\n'}
[0.121570] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/parent_prefix_path/turtle_bot_bringup\n'}
[0.124338] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.sh\n'}
[0.124418] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.dsv\n'}
[0.124458] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.sh\n'}
[0.124496] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.dsv\n'}
[0.124595] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.bash\n'}
[0.124637] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.sh\n'}
[0.124674] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.zsh\n'}
[0.124723] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.dsv\n'}
[0.124761] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv\n'}
[0.124799] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/packages/turtle_bot_bringup\n'}
[0.124838] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig.cmake\n'}
[0.124876] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig-version.cmake\n'}
[0.125068] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.xml\n'}
[0.125119] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[0.156103] (turtle_bot_bringup) JobEnded: {'identifier': 'turtle_bot_bringup', 'rc': 0}
[0.157309] (-) EventReactorShutdown: {}
