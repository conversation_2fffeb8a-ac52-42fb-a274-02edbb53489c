[0.000000] (-) TimerEvent: {}
[0.003521] (turtle_bot_description) JobQueued: {'identifier': 'turtle_bot_description', 'dependencies': OrderedDict()}
[0.003786] (turtle_bot_gazebo) JobQueued: {'identifier': 'turtle_bot_gazebo', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description')])}
[0.003823] (turtle_bot_navigation) JobQueued: {'identifier': 'turtle_bot_navigation', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle/install/turtle_bot_gazebo')])}
[0.003922] (turtle_bot_slam) JobQueued: {'identifier': 'turtle_bot_slam', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle/install/turtle_bot_gazebo')])}
[0.004007] (turtle_bot_bringup) JobQueued: {'identifier': 'turtle_bot_bringup', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle/install/turtle_bot_gazebo'), ('turtle_bot_navigation', '/home/<USER>/turtle/install/turtle_bot_navigation'), ('turtle_bot_slam', '/home/<USER>/turtle/install/turtle_bot_slam')])}
[0.004038] (turtle_bot_description) JobStarted: {'identifier': 'turtle_bot_description'}
[0.023550] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'cmake'}
[0.030065] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'build'}
[0.030183] (turtle_bot_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_description', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[0.104431] (-) TimerEvent: {}
[0.116340] (turtle_bot_description) CommandEnded: {'returncode': 0}
[0.117406] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'install'}
[0.145867] (turtle_bot_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_description'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[0.159274] (turtle_bot_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.159498] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf\n'}
[0.159560] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot_gazebo.urdf.xacro\n'}
[0.159605] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot.urdf.xacro\n'}
[0.159647] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/meshes\n'}
[0.159682] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch\n'}
[0.159716] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/display.launch.py\n'}
[0.159754] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/robot_state_publisher.launch.py\n'}
[0.159792] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config\n'}
[0.159826] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config/display.rviz\n'}
[0.159863] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/package_run_dependencies/turtle_bot_description\n'}
[0.159904] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/parent_prefix_path/turtle_bot_description\n'}
[0.159937] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.sh\n'}
[0.159970] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.dsv\n'}
[0.160004] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.sh\n'}
[0.160037] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.dsv\n'}
[0.160070] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.bash\n'}
[0.160154] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.sh\n'}
[0.160241] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.zsh\n'}
[0.160283] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.dsv\n'}
[0.160360] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv\n'}
[0.160438] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/packages/turtle_bot_description\n'}
[0.160692] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig.cmake\n'}
[0.160801] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig-version.cmake\n'}
[0.160886] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.xml\n'}
[0.164561] (turtle_bot_description) CommandEnded: {'returncode': 0}
[0.197130] (turtle_bot_description) JobEnded: {'identifier': 'turtle_bot_description', 'rc': 0}
[0.201577] (turtle_bot_gazebo) JobStarted: {'identifier': 'turtle_bot_gazebo'}
[0.214465] (-) TimerEvent: {}
[0.228937] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'cmake'}
[0.233879] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'build'}
[0.233979] (turtle_bot_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_gazebo', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_gazebo'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/opt/ros/humble')]), 'shell': False}
[0.315714] (-) TimerEvent: {}
[0.341618] (turtle_bot_gazebo) CommandEnded: {'returncode': 0}
[0.350671] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'install'}
[0.350765] (turtle_bot_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_gazebo'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_gazebo'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/opt/ros/humble')]), 'shell': False}
[0.376865] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.377318] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds\n'}
[0.377422] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds/turtle_world.world\n'}
[0.377515] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch\n'}
[0.377603] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch/gazebo.launch.py\n'}
[0.377689] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/config\n'}
[0.377774] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/package_run_dependencies/turtle_bot_gazebo\n'}
[0.377859] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/parent_prefix_path/turtle_bot_gazebo\n'}
[0.377943] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.sh\n'}
[0.378641] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.dsv\n'}
[0.378954] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.sh\n'}
[0.379331] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.dsv\n'}
[0.379439] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.bash\n'}
[0.379521] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.sh\n'}
[0.379595] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.zsh\n'}
[0.379665] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.dsv\n'}
[0.379738] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv\n'}
[0.379808] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/packages/turtle_bot_gazebo\n'}
[0.379892] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig.cmake\n'}
[0.379965] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig-version.cmake\n'}
[0.380038] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.xml\n'}
[0.385654] (turtle_bot_gazebo) CommandEnded: {'returncode': 0}
[0.408814] (turtle_bot_gazebo) JobEnded: {'identifier': 'turtle_bot_gazebo', 'rc': 0}
[0.409915] (turtle_bot_navigation) JobStarted: {'identifier': 'turtle_bot_navigation'}
[0.415474] (turtle_bot_slam) JobStarted: {'identifier': 'turtle_bot_slam'}
[0.416277] (-) TimerEvent: {}
[0.425595] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'cmake'}
[0.426493] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'build'}
[0.426886] (turtle_bot_navigation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_navigation', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_navigation'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble')]), 'shell': False}
[0.433156] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'cmake'}
[0.436654] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'build'}
[0.436720] (turtle_bot_slam) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_slam', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_slam', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_slam'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble')]), 'shell': False}
[0.484957] (turtle_bot_navigation) CommandEnded: {'returncode': 0}
[0.485550] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'install'}
[0.485571] (turtle_bot_navigation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_navigation'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_navigation'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble')]), 'shell': False}
[0.494756] (turtle_bot_slam) CommandEnded: {'returncode': 0}
[0.495322] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'install'}
[0.495363] (turtle_bot_slam) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_slam'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_slam', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_slam'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble')]), 'shell': False}
[0.496491] (turtle_bot_navigation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.496593] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/config\n'}
[0.496640] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/config/nav2_params.yaml\n'}
[0.496691] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/launch\n'}
[0.496733] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/launch/navigation.launch.py\n'}
[0.496775] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps\n'}
[0.496816] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.yaml\n'}
[0.496858] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.pgm\n'}
[0.496900] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/package_run_dependencies/turtle_bot_navigation\n'}
[0.496941] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/parent_prefix_path/turtle_bot_navigation\n'}
[0.496984] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.sh\n'}
[0.497028] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.dsv\n'}
[0.497092] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.sh\n'}
[0.497158] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.dsv\n'}
[0.497228] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.bash\n'}
[0.497276] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.sh\n'}
[0.497319] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.zsh\n'}
[0.497361] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.dsv\n'}
[0.497403] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv\n'}
[0.497447] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/packages/turtle_bot_navigation\n'}
[0.497490] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig.cmake\n'}
[0.497532] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig-version.cmake\n'}
[0.497572] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.xml\n'}
[0.500775] (turtle_bot_navigation) CommandEnded: {'returncode': 0}
[0.506635] (turtle_bot_slam) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.506804] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config\n'}
[0.506854] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config/slam_params.yaml\n'}
[0.506890] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch\n'}
[0.506926] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch/slam.launch.py\n'}
[0.506960] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/package_run_dependencies/turtle_bot_slam\n'}
[0.506995] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/parent_prefix_path/turtle_bot_slam\n'}
[0.507039] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.sh\n'}
[0.507073] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.dsv\n'}
[0.507326] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.sh\n'}
[0.507422] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.dsv\n'}
[0.508580] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.bash\n'}
[0.508681] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.sh\n'}
[0.508781] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.zsh\n'}
[0.508891] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.dsv\n'}
[0.508978] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv\n'}
[0.509029] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/packages/turtle_bot_slam\n'}
[0.509066] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig.cmake\n'}
[0.509265] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig-version.cmake\n'}
[0.509311] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.xml\n'}
[0.517660] (-) TimerEvent: {}
[0.527685] (turtle_bot_navigation) JobEnded: {'identifier': 'turtle_bot_navigation', 'rc': 0}
[0.530545] (turtle_bot_slam) CommandEnded: {'returncode': 0}
[0.549979] (turtle_bot_slam) JobEnded: {'identifier': 'turtle_bot_slam', 'rc': 0}
[0.550498] (turtle_bot_bringup) JobStarted: {'identifier': 'turtle_bot_bringup'}
[0.573160] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'cmake'}
[0.576711] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'build'}
[0.576894] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_bringup', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_bringup', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/opt/ros/humble')]), 'shell': False}
[0.618257] (-) TimerEvent: {}
[0.672707] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[0.681978] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'install'}
[0.682590] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_bringup'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_bringup', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/opt/ros/humble')]), 'shell': False}
[0.702728] (turtle_bot_bringup) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.703417] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch\n'}
[0.703722] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam.launch.py\n'}
[0.703883] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_simulation.launch.py\n'}
[0.709167] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot.rviz\n'}
[0.709493] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts\n'}
[0.709595] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/wasd_controller.py\n'}
[0.709680] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/lib/turtle_bot_bringup/wasd_controller.py\n'}
[0.710514] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/package_run_dependencies/turtle_bot_bringup\n'}
[0.711890] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/parent_prefix_path/turtle_bot_bringup\n'}
[0.712255] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.sh\n'}
[0.712348] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.dsv\n'}
[0.713356] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.sh\n'}
[0.713463] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.dsv\n'}
[0.713549] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.bash\n'}
[0.713632] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.sh\n'}
[0.715621] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.zsh\n'}
[0.716809] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.dsv\n'}
[0.717239] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv\n'}
[0.719585] (-) TimerEvent: {}
[0.719881] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/packages/turtle_bot_bringup\n'}
[0.720134] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig.cmake\n'}
[0.720230] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig-version.cmake\n'}
[0.720870] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.xml\n'}
[0.724587] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[0.763065] (turtle_bot_bringup) JobEnded: {'identifier': 'turtle_bot_bringup', 'rc': 0}
[0.766954] (-) EventReactorShutdown: {}
