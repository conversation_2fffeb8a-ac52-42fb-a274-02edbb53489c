[0.096s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.096s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7c95ac756b00>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7c95ac7565c0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7c95ac7565c0>>, mixin_verb=('build',))
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.220s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/turtle'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_meta']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_meta'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ros'
[0.236s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_bringup' with type 'ros.ament_cmake' and name 'turtle_bot_bringup'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore_ament_install'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_pkg']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_pkg'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_meta']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_meta'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ros']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ros'
[0.236s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_description' with type 'ros.ament_cmake' and name 'turtle_bot_description'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ros'
[0.238s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_gazebo' with type 'ros.ament_cmake' and name 'turtle_bot_gazebo'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore_ament_install'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_pkg']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_pkg'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_meta']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_meta'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ros']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ros'
[0.239s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_navigation' with type 'ros.ament_cmake' and name 'turtle_bot_navigation'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ignore', 'ignore_ament_install']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore_ament_install'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_pkg']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_pkg'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_meta']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_meta'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ros']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ros'
[0.241s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_slam' with type 'ros.ament_cmake' and name 'turtle_bot_slam'
[0.241s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.242s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.242s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.242s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.242s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.277s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 5 installed packages in /home/<USER>/turtle/install
[0.278s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 430 installed packages in /opt/ros/humble
[0.279s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_args' from command line to 'None'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target' from command line to 'None'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.316s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_description', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_description', 'symlink_install': False, 'test_result_base': None}
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_args' from command line to 'None'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target' from command line to 'None'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[0.316s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.316s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_gazebo', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_gazebo', 'symlink_install': False, 'test_result_base': None}
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_args' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_cache' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_first' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_force_configure' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'ament_cmake_args' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_cmake_args' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.317s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_navigation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_navigation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_navigation', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_navigation', 'symlink_install': False, 'test_result_base': None}
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_args' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_cache' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_first' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_force_configure' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'ament_cmake_args' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_cmake_args' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.317s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_slam' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_slam', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_slam', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_slam', 'symlink_install': False, 'test_result_base': None}
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_args' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_first' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_force_configure' from command line to 'False'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'ament_cmake_args' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[0.317s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.317s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_bringup', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_bringup', 'symlink_install': False, 'test_result_base': None}
[0.317s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.319s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.319s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_description' with build type 'ament_cmake'
[0.320s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_description'
[0.322s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.322s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.322s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.352s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
[0.436s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
[0.466s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
[0.482s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[0.484s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
[0.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake module files
[0.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake config files
[0.486s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[0.486s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[0.487s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[0.488s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/python3.10/site-packages'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[0.490s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[0.492s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[0.494s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[0.495s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[0.496s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[0.498s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake module files
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake config files
[0.499s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[0.500s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[0.500s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[0.501s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[0.502s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[0.502s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/python3.10/site-packages'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[0.504s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[0.505s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[0.507s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[0.508s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[0.510s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[0.511s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[0.514s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_gazebo' with build type 'ament_cmake'
[0.514s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_gazebo'
[0.515s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.515s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.559s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[0.661s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[0.670s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
[0.702s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[0.702s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake module files
[0.703s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake config files
[0.704s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[0.704s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[0.705s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
[0.706s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[0.707s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[0.709s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[0.709s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[0.710s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[0.710s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[0.711s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[0.712s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[0.713s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[0.715s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[0.716s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[0.717s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[0.718s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[0.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake module files
[0.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake config files
[0.720s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[0.720s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[0.721s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[0.722s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[0.722s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[0.723s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[0.723s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[0.723s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[0.724s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[0.724s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[0.725s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[0.726s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[0.726s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[0.727s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[0.729s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_navigation' with build type 'ament_cmake'
[0.729s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_navigation'
[0.730s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.730s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.734s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_slam' with build type 'ament_cmake'
[0.734s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_slam'
[0.735s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.735s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.746s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_navigation -- -j4 -l4
[0.757s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[0.804s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_navigation -- -j4 -l4
[0.804s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_navigation
[0.814s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[0.815s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
[0.817s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[0.818s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake module files
[0.818s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake config files
[0.818s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[0.818s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[0.819s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[0.820s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_navigation
[0.820s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[0.821s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[0.821s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[0.821s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/python3.10/site-packages'
[0.822s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[0.822s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[0.823s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[0.823s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[0.826s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[0.829s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[0.830s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[0.832s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[0.832s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake module files
[0.833s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake config files
[0.833s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[0.834s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[0.835s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[0.837s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[0.839s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[0.839s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[0.839s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/python3.10/site-packages'
[0.839s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[0.840s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[0.841s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[0.842s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[0.843s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[0.844s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[0.845s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[0.848s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[0.849s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake module files
[0.850s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
[0.851s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake config files
[0.851s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[0.851s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[0.852s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[0.853s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[0.854s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[0.854s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[0.855s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/python3.10/site-packages'
[0.855s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[0.855s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[0.856s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[0.857s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[0.858s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[0.859s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[0.860s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[0.861s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[0.861s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake module files
[0.861s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake config files
[0.862s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[0.862s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[0.862s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[0.863s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[0.864s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[0.864s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[0.864s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/python3.10/site-packages'
[0.864s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[0.864s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[0.865s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[0.866s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[0.866s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[0.867s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[0.867s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[0.868s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_bringup' with build type 'ament_cmake'
[0.868s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_bringup'
[0.868s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.868s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.898s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_bringup -- -j4 -l4
[0.993s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_bringup -- -j4 -l4
[1.002s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_bringup
[1.041s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[1.042s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake module files
[1.044s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake config files
[1.046s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/home/<USER>/turtle/install/turtle_bot_bringup:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_bringup
[1.047s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[1.048s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[1.049s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[1.051s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[1.052s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib'
[1.053s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[1.053s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[1.053s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/python3.10/site-packages'
[1.054s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[1.055s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[1.057s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[1.059s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[1.061s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[1.062s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[1.064s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[1.066s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[1.067s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake module files
[1.067s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake config files
[1.067s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[1.068s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[1.068s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[1.069s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[1.071s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib'
[1.071s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[1.071s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[1.072s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/python3.10/site-packages'
[1.072s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[1.072s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[1.073s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[1.075s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[1.076s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[1.078s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[1.079s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[1.082s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.083s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.083s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.083s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.097s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.097s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.098s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.137s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.138s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.ps1'
[1.141s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle/install/_local_setup_util_ps1.py'
[1.145s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.ps1'
[1.147s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.sh'
[1.148s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle/install/_local_setup_util_sh.py'
[1.150s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.sh'
[1.152s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.bash'
[1.153s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.bash'
[1.155s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.zsh'
[1.156s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.zsh'
