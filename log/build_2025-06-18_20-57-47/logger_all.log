[0.770s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.770s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x770f18152b30>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x770f181525f0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x770f181525f0>>, mixin_verb=('build',))
[1.328s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.328s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.329s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.329s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.329s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.329s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.329s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/turtle'
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.357s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ignore', 'ignore_ament_install']
[1.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore'
[1.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore_ament_install'
[1.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_pkg']
[1.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_pkg'
[1.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_meta']
[1.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_meta'
[1.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ros']
[1.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ros'
[1.366s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_bringup' with type 'ros.ament_cmake' and name 'turtle_bot_bringup'
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ignore', 'ignore_ament_install']
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore'
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore_ament_install'
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_pkg']
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_pkg'
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_meta']
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_meta'
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ros']
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ros'
[1.369s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_description' with type 'ros.ament_cmake' and name 'turtle_bot_description'
[1.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ignore', 'ignore_ament_install']
[1.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore'
[1.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore_ament_install'
[1.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_pkg']
[1.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_pkg'
[1.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_meta']
[1.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_meta'
[1.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ros']
[1.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ros'
[1.373s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_gazebo' with type 'ros.ament_cmake' and name 'turtle_bot_gazebo'
[1.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ignore', 'ignore_ament_install']
[1.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore'
[1.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore_ament_install'
[1.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_pkg']
[1.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_pkg'
[1.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_meta']
[1.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_meta'
[1.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ros']
[1.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ros'
[1.376s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_navigation' with type 'ros.ament_cmake' and name 'turtle_bot_navigation'
[1.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ignore', 'ignore_ament_install']
[1.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore'
[1.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore_ament_install'
[1.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_pkg']
[1.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_pkg'
[1.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_meta']
[1.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_meta'
[1.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ros']
[1.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ros'
[1.381s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_slam' with type 'ros.ament_cmake' and name 'turtle_bot_slam'
[1.381s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.381s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.382s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.382s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.382s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.459s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.459s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.474s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 430 installed packages in /opt/ros/humble
[1.477s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[1.548s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_args' from command line to 'None'
[1.548s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target' from command line to 'None'
[1.549s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.549s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_cache' from command line to 'False'
[1.549s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_first' from command line to 'False'
[1.549s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_force_configure' from command line to 'False'
[1.549s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'ament_cmake_args' from command line to 'None'
[1.549s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_cmake_args' from command line to 'None'
[1.549s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.549s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_description', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_description', 'symlink_install': False, 'test_result_base': None}
[1.549s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_args' from command line to 'None'
[1.550s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target' from command line to 'None'
[1.550s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.550s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[1.550s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[1.550s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[1.550s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[1.550s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[1.550s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.550s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_gazebo', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_gazebo', 'symlink_install': False, 'test_result_base': None}
[1.551s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_args' from command line to 'None'
[1.551s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target' from command line to 'None'
[1.551s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.551s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_cache' from command line to 'False'
[1.551s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_first' from command line to 'False'
[1.551s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_force_configure' from command line to 'False'
[1.551s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'ament_cmake_args' from command line to 'None'
[1.551s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_cmake_args' from command line to 'None'
[1.551s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.551s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_navigation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_navigation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_navigation', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_navigation', 'symlink_install': False, 'test_result_base': None}
[1.552s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_args' from command line to 'None'
[1.552s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target' from command line to 'None'
[1.552s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.552s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_cache' from command line to 'False'
[1.552s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_first' from command line to 'False'
[1.552s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_force_configure' from command line to 'False'
[1.552s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'ament_cmake_args' from command line to 'None'
[1.552s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_cmake_args' from command line to 'None'
[1.552s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.552s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_slam' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_slam', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_slam', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_slam', 'symlink_install': False, 'test_result_base': None}
[1.553s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_args' from command line to 'None'
[1.553s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target' from command line to 'None'
[1.553s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.553s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[1.553s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_first' from command line to 'False'
[1.553s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_force_configure' from command line to 'False'
[1.553s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'ament_cmake_args' from command line to 'None'
[1.553s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[1.553s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.553s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_bringup', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_bringup', 'symlink_install': False, 'test_result_base': None}
[1.554s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.559s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.559s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_description' with build type 'ament_cmake'
[1.560s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_description'
[1.578s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.578s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.578s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.611s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
[2.230s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
[2.250s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
[2.277s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[2.278s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
[2.295s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake module files
[2.295s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake config files
[2.297s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[2.298s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[2.301s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[2.303s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[2.306s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[2.306s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[2.306s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/python3.10/site-packages'
[2.306s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[2.307s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[2.308s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[2.310s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[2.311s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[2.313s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[2.314s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[2.315s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[2.316s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake module files
[2.316s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake config files
[2.316s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[2.316s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[2.317s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[2.317s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[2.318s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[2.318s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[2.318s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/python3.10/site-packages'
[2.319s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[2.319s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[2.333s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[2.333s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[2.334s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[2.341s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[2.342s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[2.343s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_gazebo' with build type 'ament_cmake'
[2.343s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_gazebo'
[2.343s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.344s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.381s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[2.447s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[2.453s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
[2.478s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[2.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake module files
[2.479s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake config files
[2.480s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
[2.480s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[2.481s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[2.482s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[2.484s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[2.485s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[2.485s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[2.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[2.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[2.486s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[2.488s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[2.489s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[2.490s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[2.491s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[2.492s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[2.495s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[2.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake module files
[2.496s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake config files
[2.496s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[2.496s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[2.496s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[2.498s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[2.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[2.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[2.500s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[2.500s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[2.500s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[2.501s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[2.502s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[2.503s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[2.504s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[2.505s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[2.506s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_navigation' with build type 'ament_cmake'
[2.506s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_navigation'
[2.506s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.506s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.523s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_slam' with build type 'ament_cmake'
[2.523s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_slam'
[2.524s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.524s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.551s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_navigation -- -j4 -l4
[2.554s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[2.651s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_navigation -- -j4 -l4
[2.664s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_navigation
[2.680s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[2.696s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
[2.699s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[2.700s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake module files
[2.701s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_navigation
[2.706s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake config files
[2.708s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[2.708s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[2.710s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[2.711s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[2.714s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[2.717s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[2.717s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/python3.10/site-packages'
[2.720s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[2.721s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[2.722s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[2.723s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[2.724s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[2.724s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[2.725s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[2.726s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[2.726s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake module files
[2.728s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake config files
[2.730s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[2.730s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[2.732s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[2.735s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[2.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[2.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[2.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/python3.10/site-packages'
[2.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[2.739s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[2.741s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[2.742s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[2.744s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[2.745s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[2.746s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[2.750s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[2.751s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake module files
[2.752s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
[2.752s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake config files
[2.753s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[2.754s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[2.756s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[2.757s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[2.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[2.760s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[2.760s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/python3.10/site-packages'
[2.760s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[2.760s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[2.762s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[2.763s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[2.764s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[2.764s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[2.765s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[2.768s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[2.769s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake module files
[2.769s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake config files
[2.769s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[2.769s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[2.770s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[2.771s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[2.773s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[2.773s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[2.773s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/python3.10/site-packages'
[2.774s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[2.775s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[2.776s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[2.777s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[2.780s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[2.782s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[2.783s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[2.784s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_bringup' with build type 'ament_cmake'
[2.784s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_bringup'
[2.784s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.784s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.877s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_bringup -- -j4 -l4
[2.953s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_bringup -- -j4 -l4
[2.963s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_bringup
[2.994s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[2.994s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake module files
[2.995s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_bringup
[2.996s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake config files
[3.008s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[3.008s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[3.009s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[3.010s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[3.011s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib'
[3.011s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[3.011s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[3.011s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/python3.10/site-packages'
[3.011s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[3.012s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[3.012s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[3.013s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[3.013s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[3.014s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[3.015s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[3.017s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[3.018s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake module files
[3.018s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake config files
[3.018s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[3.018s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[3.019s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[3.020s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[3.020s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib'
[3.020s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[3.020s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[3.020s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/python3.10/site-packages'
[3.021s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[3.021s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[3.022s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[3.022s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[3.023s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[3.023s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[3.024s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[3.035s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[3.037s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[3.038s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[3.038s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[3.051s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[3.051s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[3.051s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[3.064s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[3.065s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.ps1'
[3.067s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle/install/_local_setup_util_ps1.py'
[3.073s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.ps1'
[3.076s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.sh'
[3.077s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle/install/_local_setup_util_sh.py'
[3.078s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.sh'
[3.085s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.bash'
[3.087s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.bash'
[3.090s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.zsh'
[3.092s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.zsh'
