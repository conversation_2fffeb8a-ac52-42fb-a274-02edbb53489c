[0.026s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[0.152s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[0.168s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
[0.186s] -- Install configuration: ""
[0.190s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config
[0.190s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config/slam_params.yaml
[0.191s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch
[0.191s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch/slam.launch.py
[0.199s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/package_run_dependencies/turtle_bot_slam
[0.200s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/parent_prefix_path/turtle_bot_slam
[0.201s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.sh
[0.201s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.dsv
[0.202s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.sh
[0.202s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.dsv
[0.203s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.bash
[0.203s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.sh
[0.203s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.zsh
[0.203s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.dsv
[0.203s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv
[0.208s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/packages/turtle_bot_slam
[0.208s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig.cmake
[0.208s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig-version.cmake
[0.209s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.xml
[0.224s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
