[0.000000] (-) TimerEvent: {}
[0.004544] (turtle_bot_description) JobQueued: {'identifier': 'turtle_bot_description', 'dependencies': OrderedDict()}
[0.005279] (turtle_bot_gazebo) JobQueued: {'identifier': 'turtle_bot_gazebo', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description')])}
[0.005415] (turtle_bot_navigation) JobQueued: {'identifier': 'turtle_bot_navigation', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle/install/turtle_bot_gazebo')])}
[0.005454] (turtle_bot_slam) JobQueued: {'identifier': 'turtle_bot_slam', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle/install/turtle_bot_gazebo')])}
[0.005478] (turtle_bot_bringup) JobQueued: {'identifier': 'turtle_bot_bringup', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle/install/turtle_bot_gazebo'), ('turtle_bot_navigation', '/home/<USER>/turtle/install/turtle_bot_navigation'), ('turtle_bot_slam', '/home/<USER>/turtle/install/turtle_bot_slam')])}
[0.005510] (turtle_bot_description) JobStarted: {'identifier': 'turtle_bot_description'}
[0.039331] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'cmake'}
[0.042941] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'build'}
[0.044298] (turtle_bot_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_description', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.099119] (-) TimerEvent: {}
[0.199855] (-) TimerEvent: {}
[0.322708] (-) TimerEvent: {}
[0.423799] (-) TimerEvent: {}
[0.524648] (-) TimerEvent: {}
[0.625205] (-) TimerEvent: {}
[0.673706] (turtle_bot_description) CommandEnded: {'returncode': 0}
[0.680681] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'install'}
[0.693711] (turtle_bot_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_description'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.705996] (turtle_bot_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.709035] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf\n'}
[0.710062] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot_gazebo.urdf.xacro\n'}
[0.710127] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot.urdf.xacro\n'}
[0.710170] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/meshes\n'}
[0.710730] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch\n'}
[0.711734] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/display.launch.py\n'}
[0.711902] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/robot_state_publisher.launch.py\n'}
[0.711943] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config\n'}
[0.713089] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config/display.rviz\n'}
[0.714278] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/package_run_dependencies/turtle_bot_description\n'}
[0.715308] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/parent_prefix_path/turtle_bot_description\n'}
[0.715475] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.sh\n'}
[0.715570] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.dsv\n'}
[0.716470] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.sh\n'}
[0.716545] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.dsv\n'}
[0.717083] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.bash\n'}
[0.717155] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.sh\n'}
[0.717652] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.zsh\n'}
[0.718401] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.dsv\n'}
[0.718449] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv\n'}
[0.718484] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/packages/turtle_bot_description\n'}
[0.718520] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig.cmake\n'}
[0.718555] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig-version.cmake\n'}
[0.718590] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.xml\n'}
[0.721289] (turtle_bot_description) CommandEnded: {'returncode': 0}
[0.725970] (-) TimerEvent: {}
[0.787927] (turtle_bot_description) JobEnded: {'identifier': 'turtle_bot_description', 'rc': 0}
[0.795568] (turtle_bot_gazebo) JobStarted: {'identifier': 'turtle_bot_gazebo'}
[0.811028] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'cmake'}
[0.823758] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'build'}
[0.823889] (turtle_bot_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_gazebo', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_gazebo'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[0.827915] (-) TimerEvent: {}
[0.890123] (turtle_bot_gazebo) CommandEnded: {'returncode': 0}
[0.896851] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'install'}
[0.896910] (turtle_bot_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_gazebo'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_gazebo'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[0.906210] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.908367] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds\n'}
[0.911488] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds/turtle_world.world\n'}
[0.912802] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch\n'}
[0.913234] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch/gazebo.launch.py\n'}
[0.913279] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/config\n'}
[0.915331] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/package_run_dependencies/turtle_bot_gazebo\n'}
[0.916033] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/parent_prefix_path/turtle_bot_gazebo\n'}
[0.916300] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.sh\n'}
[0.916368] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.dsv\n'}
[0.916407] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.sh\n'}
[0.916444] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.dsv\n'}
[0.916480] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.bash\n'}
[0.917123] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.sh\n'}
[0.917172] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.zsh\n'}
[0.917223] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.dsv\n'}
[0.917260] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv\n'}
[0.918357] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/packages/turtle_bot_gazebo\n'}
[0.918452] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig.cmake\n'}
[0.918490] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig-version.cmake\n'}
[0.918962] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.xml\n'}
[0.922733] (turtle_bot_gazebo) CommandEnded: {'returncode': 0}
[0.928128] (-) TimerEvent: {}
[0.949091] (turtle_bot_gazebo) JobEnded: {'identifier': 'turtle_bot_gazebo', 'rc': 0}
[0.953135] (turtle_bot_navigation) JobStarted: {'identifier': 'turtle_bot_navigation'}
[0.971750] (turtle_bot_slam) JobStarted: {'identifier': 'turtle_bot_slam'}
[0.984941] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'cmake'}
[0.991064] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'build'}
[0.991188] (turtle_bot_navigation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_navigation', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_navigation'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[0.994780] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'cmake'}
[0.996644] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'build'}
[0.996929] (turtle_bot_slam) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_slam', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_slam', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_slam'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[1.030767] (-) TimerEvent: {}
[1.094516] (turtle_bot_navigation) CommandEnded: {'returncode': 0}
[1.107567] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'install'}
[1.107785] (turtle_bot_navigation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_navigation'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_navigation'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[1.121795] (turtle_bot_navigation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.123180] (turtle_bot_slam) CommandEnded: {'returncode': 0}
[1.126480] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/config\n'}
[1.128281] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/config/nav2_params.yaml\n'}
[1.128447] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/launch\n'}
[1.129493] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/launch/navigation.launch.py\n'}
[1.129777] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps\n'}
[1.131250] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.yaml\n'}
[1.131432] (-) TimerEvent: {}
[1.131535] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.pgm\n'}
[1.134554] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'install'}
[1.134788] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/package_run_dependencies/turtle_bot_navigation\n'}
[1.139318] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/parent_prefix_path/turtle_bot_navigation\n'}
[1.139535] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.sh\n'}
[1.139584] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.dsv\n'}
[1.139626] (turtle_bot_slam) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_slam'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_slam', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_slam'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[1.139911] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.sh\n'}
[1.139961] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.dsv\n'}
[1.140001] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.bash\n'}
[1.140040] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.sh\n'}
[1.140077] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.zsh\n'}
[1.140114] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.dsv\n'}
[1.140155] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv\n'}
[1.140192] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/packages/turtle_bot_navigation\n'}
[1.140229] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig.cmake\n'}
[1.140267] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig-version.cmake\n'}
[1.140306] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.xml\n'}
[1.144999] (turtle_bot_navigation) CommandEnded: {'returncode': 0}
[1.157341] (turtle_bot_slam) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.161832] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config\n'}
[1.162142] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config/slam_params.yaml\n'}
[1.162263] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch\n'}
[1.162564] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch/slam.launch.py\n'}
[1.170919] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/package_run_dependencies/turtle_bot_slam\n'}
[1.171833] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/parent_prefix_path/turtle_bot_slam\n'}
[1.172403] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.sh\n'}
[1.172739] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.dsv\n'}
[1.173271] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.sh\n'}
[1.173337] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.dsv\n'}
[1.174514] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.bash\n'}
[1.174596] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.sh\n'}
[1.174637] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.zsh\n'}
[1.174969] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.dsv\n'}
[1.175013] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv\n'}
[1.179978] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/packages/turtle_bot_slam\n'}
[1.180105] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig.cmake\n'}
[1.180151] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig-version.cmake\n'}
[1.180199] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.xml\n'}
[1.190874] (turtle_bot_navigation) JobEnded: {'identifier': 'turtle_bot_navigation', 'rc': 0}
[1.195481] (turtle_bot_slam) CommandEnded: {'returncode': 0}
[1.229310] (turtle_bot_slam) JobEnded: {'identifier': 'turtle_bot_slam', 'rc': 0}
[1.230213] (turtle_bot_bringup) JobStarted: {'identifier': 'turtle_bot_bringup'}
[1.234113] (-) TimerEvent: {}
[1.316140] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'cmake'}
[1.320099] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'build'}
[1.320156] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_bringup', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_bringup', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[1.334773] (-) TimerEvent: {}
[1.396608] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[1.406797] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'install'}
[1.406878] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_bringup'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_bringup', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1466'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1433,unix/Steri:/tmp/.ICE-unix/1433'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/17d4bf46_84a8_4ba6_bbcb_5e02e89b4c99'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.8D0Q82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.117'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[1.422773] (turtle_bot_bringup) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.425133] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch\n'}
[1.425960] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam.launch.py\n'}
[1.426478] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_simulation.launch.py\n'}
[1.426683] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot.rviz\n'}
[1.426735] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts\n'}
[1.427380] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/wasd_controller.py\n'}
[1.428756] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/lib/turtle_bot_bringup/wasd_controller.py\n'}
[1.430347] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/package_run_dependencies/turtle_bot_bringup\n'}
[1.431099] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/parent_prefix_path/turtle_bot_bringup\n'}
[1.431535] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.sh\n'}
[1.431873] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.dsv\n'}
[1.431961] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.sh\n'}
[1.432394] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.dsv\n'}
[1.432500] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.bash\n'}
[1.432569] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.sh\n'}
[1.432633] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.zsh\n'}
[1.432778] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.dsv\n'}
[1.433113] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv\n'}
[1.433980] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/packages/turtle_bot_bringup\n'}
[1.436456] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig.cmake\n'}
[1.436677] (-) TimerEvent: {}
[1.436955] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig-version.cmake\n'}
[1.437108] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.xml\n'}
[1.438730] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[1.480185] (turtle_bot_bringup) JobEnded: {'identifier': 'turtle_bot_bringup', 'rc': 0}
[1.484783] (-) EventReactorShutdown: {}
