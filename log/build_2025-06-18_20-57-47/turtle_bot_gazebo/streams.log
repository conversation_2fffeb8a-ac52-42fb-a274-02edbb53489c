[0.029s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[0.095s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[0.101s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
[0.111s] -- Install configuration: ""
[0.113s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds
[0.117s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds/turtle_world.world
[0.118s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch
[0.118s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch/gazebo.launch.py
[0.118s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/config
[0.120s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/package_run_dependencies/turtle_bot_gazebo
[0.121s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/parent_prefix_path/turtle_bot_gazebo
[0.121s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.sh
[0.121s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.dsv
[0.121s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.sh
[0.121s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.dsv
[0.121s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.bash
[0.122s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.sh
[0.122s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.zsh
[0.122s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.dsv
[0.122s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv
[0.123s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/packages/turtle_bot_gazebo
[0.123s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig.cmake
[0.123s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig-version.cmake
[0.123s] -- Up-to-date: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.xml
[0.128s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
