[0.096s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.097s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x78413674eef0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x78413674e920>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x78413674e920>>, mixin_verb=('build',))
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.247s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.247s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/turtle2'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore_ament_install'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_pkg']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_pkg'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_meta']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_meta'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ros']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ros'
[0.261s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_bringup' with type 'ros.ament_cmake' and name 'turtle_bot_bringup'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ignore', 'ignore_ament_install']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore_ament_install'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_pkg']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_pkg'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_meta']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_meta'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ros']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ros'
[0.263s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_description' with type 'ros.ament_cmake' and name 'turtle_bot_description'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ignore', 'ignore_ament_install']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore_ament_install'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_pkg']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_pkg'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_meta']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_meta'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ros']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ros'
[0.263s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_gazebo' with type 'ros.ament_cmake' and name 'turtle_bot_gazebo'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore_ament_install'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_pkg']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_pkg'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_meta']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_meta'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ros']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ros'
[0.265s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_navigation' with type 'ros.ament_cmake' and name 'turtle_bot_navigation'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ignore', 'ignore_ament_install']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore_ament_install'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_pkg']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_pkg'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_meta']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_meta'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ros']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ros'
[0.267s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_slam' with type 'ros.ament_cmake' and name 'turtle_bot_slam'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['ignore', 'ignore_ament_install']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ignore'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ignore_ament_install'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['colcon_pkg']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'colcon_pkg'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['colcon_meta']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'colcon_meta'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['ros']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ros'
[0.268s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_uv_system' with type 'ros.ament_python' and name 'turtle_bot_uv_system'
[0.269s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.269s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.269s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.269s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.269s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.302s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.302s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.304s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 6 installed packages in /home/<USER>/turtle2/install
[0.306s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 430 installed packages in /opt/ros/humble
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.351s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_args' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.352s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_description', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_description', 'symlink_install': False, 'test_result_base': None}
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_args' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_target' from command line to 'None'
[0.352s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_clean_cache' from command line to 'False'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_clean_first' from command line to 'False'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_force_configure' from command line to 'False'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'ament_cmake_args' from command line to 'None'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'catkin_cmake_args' from command line to 'None'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.353s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_uv_system' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_uv_system', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_uv_system', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_uv_system', 'symlink_install': False, 'test_result_base': None}
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_args' from command line to 'None'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target' from command line to 'None'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[0.353s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.353s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_gazebo', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_gazebo', 'symlink_install': False, 'test_result_base': None}
[0.354s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_args' from command line to 'None'
[0.354s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target' from command line to 'None'
[0.354s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.354s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_cache' from command line to 'False'
[0.354s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_first' from command line to 'False'
[0.354s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_force_configure' from command line to 'False'
[0.354s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'ament_cmake_args' from command line to 'None'
[0.354s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_cmake_args' from command line to 'None'
[0.354s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.354s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_navigation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_navigation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_navigation', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_navigation', 'symlink_install': False, 'test_result_base': None}
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_args' from command line to 'None'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target' from command line to 'None'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_cache' from command line to 'False'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_first' from command line to 'False'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_force_configure' from command line to 'False'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'ament_cmake_args' from command line to 'None'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_cmake_args' from command line to 'None'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.355s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_slam' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_slam', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_slam', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_slam', 'symlink_install': False, 'test_result_base': None}
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_args' from command line to 'None'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target' from command line to 'None'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_first' from command line to 'False'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_force_configure' from command line to 'False'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'ament_cmake_args' from command line to 'None'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[0.355s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.355s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_bringup', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_bringup', 'symlink_install': False, 'test_result_base': None}
[0.355s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.357s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.358s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_description' with build type 'ament_cmake'
[0.358s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_description'
[0.361s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.361s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.362s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.366s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_uv_system' with build type 'ament_python'
[0.366s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_uv_system', 'ament_prefix_path')
[0.366s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/ament_prefix_path.ps1'
[0.368s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/ament_prefix_path.dsv'
[0.368s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/ament_prefix_path.sh'
[0.369s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.369s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.383s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_description -- -j4 -l4
[0.637s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/turtle2/src/turtle_bot_uv_system'
[0.638s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.638s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.643s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_description -- -j4 -l4
[0.655s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_description
[0.668s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[0.670s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_description
[0.671s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description' for CMake module files
[0.672s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description' for CMake config files
[0.672s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[0.672s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[0.673s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[0.674s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[0.675s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/bin'
[0.675s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[0.675s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/lib/python3.10/site-packages'
[0.675s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/bin'
[0.676s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[0.677s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[0.679s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[0.680s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[0.680s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[0.681s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[0.682s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[0.683s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description' for CMake module files
[0.683s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description' for CMake config files
[0.683s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[0.684s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[0.684s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[0.685s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[0.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/bin'
[0.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[0.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/lib/python3.10/site-packages'
[0.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/bin'
[0.687s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[0.688s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[0.689s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[0.690s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[0.691s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[0.692s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[0.693s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_gazebo' with build type 'ament_cmake'
[0.693s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_gazebo'
[0.694s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.694s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.710s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_gazebo': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_gazebo -- -j4 -l4
[0.770s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_gazebo' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_gazebo -- -j4 -l4
[0.771s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_gazebo': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_gazebo
[0.786s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[0.787s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo' for CMake module files
[0.787s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo' for CMake config files
[0.787s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[0.787s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[0.790s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_gazebo' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_gazebo
[0.790s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[0.792s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[0.793s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/bin'
[0.793s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[0.797s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[0.798s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/bin'
[0.798s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[0.799s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[0.799s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[0.800s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[0.801s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[0.801s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[0.802s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[0.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo' for CMake module files
[0.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo' for CMake config files
[0.803s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[0.803s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[0.804s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[0.805s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[0.806s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/bin'
[0.806s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[0.806s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[0.807s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/bin'
[0.807s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[0.808s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[0.812s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[0.813s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[0.813s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[0.814s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[0.816s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_navigation' with build type 'ament_cmake'
[0.817s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_navigation'
[0.817s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.818s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.824s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_slam' with build type 'ament_cmake'
[0.825s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_slam'
[0.825s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.826s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.852s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_navigation': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_navigation -- -j4 -l4
[0.869s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_slam': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_slam -- -j4 -l4
[0.930s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_slam' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_slam -- -j4 -l4
[0.930s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_slam': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_slam
[0.943s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_navigation' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_navigation -- -j4 -l4
[0.948s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_navigation': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_navigation
[0.949s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[0.950s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam' for CMake module files
[0.950s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam' for CMake config files
[0.950s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[0.950s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[0.952s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_slam' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_slam
[0.952s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[0.953s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[0.954s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/bin'
[0.954s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[0.954s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/lib/python3.10/site-packages'
[0.954s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/bin'
[0.955s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[0.956s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[0.956s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[0.958s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[0.959s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[0.964s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[0.965s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[0.965s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam' for CMake module files
[0.967s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam' for CMake config files
[0.967s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[0.967s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[0.968s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[0.969s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[0.970s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/bin'
[0.970s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[0.970s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/lib/python3.10/site-packages'
[0.970s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/bin'
[0.970s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[0.971s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[0.972s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[0.974s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[0.975s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[0.976s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[0.978s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[0.978s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation' for CMake module files
[0.980s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation' for CMake config files
[0.981s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[0.981s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[0.982s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_navigation' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_navigation
[0.983s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[0.984s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[0.985s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/bin'
[0.986s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[0.986s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/lib/python3.10/site-packages'
[0.986s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/bin'
[0.987s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[0.988s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[0.989s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[0.989s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[0.990s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[0.991s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[0.992s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[0.992s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation' for CMake module files
[0.993s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation' for CMake config files
[0.993s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[0.993s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[0.994s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[0.995s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[0.997s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/bin'
[0.997s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[0.997s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/lib/python3.10/site-packages'
[0.997s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/bin'
[0.998s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[0.999s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[1.000s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[1.000s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[1.004s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[1.009s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[1.012s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_bringup' with build type 'ament_cmake'
[1.013s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_bringup'
[1.013s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.013s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.047s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_bringup': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_bringup -- -j4 -l4
[1.106s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_bringup -- -j4 -l4
[1.108s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_bringup': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_bringup
[1.109s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/src/turtle_bot_uv_system': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
[1.120s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[1.121s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_bringup
[1.121s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake module files
[1.122s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake config files
[1.122s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[1.123s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[1.123s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[1.124s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[1.125s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib'
[1.125s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[1.126s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[1.126s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/python3.10/site-packages'
[1.126s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[1.126s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[1.127s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[1.128s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[1.129s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[1.129s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[1.130s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[1.131s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[1.131s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake module files
[1.132s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake config files
[1.132s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[1.132s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[1.133s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[1.133s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[1.134s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib'
[1.135s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[1.135s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[1.135s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/python3.10/site-packages'
[1.135s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[1.135s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[1.137s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[1.137s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[1.138s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[1.139s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[1.140s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[1.364s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system' for CMake module files
[1.365s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/src/turtle_bot_uv_system' returned '0': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
[1.365s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system' for CMake config files
[1.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib'
[1.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/bin'
[1.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/pkgconfig/turtle_bot_uv_system.pc'
[1.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages'
[1.366s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_uv_system', 'pythonpath')
[1.367s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/pythonpath.ps1'
[1.368s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/pythonpath.dsv'
[1.369s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/pythonpath.sh'
[1.369s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/bin'
[1.370s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_uv_system)
[1.370s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.ps1'
[1.371s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.dsv'
[1.372s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.sh'
[1.373s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.bash'
[1.375s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.zsh'
[1.376s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_uv_system/share/colcon-core/packages/turtle_bot_uv_system)
[1.377s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.378s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.378s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.378s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.390s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.391s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.391s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.413s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.413s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.ps1'
[1.414s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle2/install/_local_setup_util_ps1.py'
[1.416s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.ps1'
[1.417s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.sh'
[1.418s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle2/install/_local_setup_util_sh.py'
[1.419s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.sh'
[1.420s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.bash'
[1.420s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.bash'
[1.422s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.zsh'
[1.423s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.zsh'
