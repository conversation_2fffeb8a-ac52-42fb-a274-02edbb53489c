[0.000000] (-) TimerEvent: {}
[0.000229] (turtle_bot_description) JobQueued: {'identifier': 'turtle_bot_description', 'dependencies': OrderedDict()}
[0.000657] (turtle_bot_uv_system) JobQueued: {'identifier': 'turtle_bot_uv_system', 'dependencies': OrderedDict()}
[0.000717] (turtle_bot_gazebo) JobQueued: {'identifier': 'turtle_bot_gazebo', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle2/install/turtle_bot_description')])}
[0.000734] (turtle_bot_navigation) JobQueued: {'identifier': 'turtle_bot_navigation', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle2/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle2/install/turtle_bot_gazebo')])}
[0.000792] (turtle_bot_slam) JobQueued: {'identifier': 'turtle_bot_slam', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle2/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle2/install/turtle_bot_gazebo')])}
[0.000809] (turtle_bot_bringup) JobQueued: {'identifier': 'turtle_bot_bringup', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle2/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle2/install/turtle_bot_gazebo'), ('turtle_bot_navigation', '/home/<USER>/turtle2/install/turtle_bot_navigation'), ('turtle_bot_slam', '/home/<USER>/turtle2/install/turtle_bot_slam')])}
[0.001801] (turtle_bot_description) JobStarted: {'identifier': 'turtle_bot_description'}
[0.009972] (turtle_bot_uv_system) JobStarted: {'identifier': 'turtle_bot_uv_system'}
[0.017827] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'cmake'}
[0.019573] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'build'}
[0.020013] (turtle_bot_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle2/build/turtle_bot_description', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.101350] (-) TimerEvent: {}
[0.203328] (-) TimerEvent: {}
[0.285801] (turtle_bot_description) CommandEnded: {'returncode': 0}
[0.287264] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'install'}
[0.297730] (turtle_bot_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle2/build/turtle_bot_description'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.303477] (-) TimerEvent: {}
[0.308218] (turtle_bot_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.308411] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/urdf\n'}
[0.308456] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot_gazebo.urdf.xacro\n'}
[0.308499] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot.urdf.xacro\n'}
[0.308537] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/meshes\n'}
[0.308574] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/launch\n'}
[0.308616] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/launch/display.launch.py\n'}
[0.308657] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/launch/robot_state_publisher.launch.py\n'}
[0.308905] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/config\n'}
[0.308974] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/config/display.rviz\n'}
[0.309013] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/ament_index/resource_index/package_run_dependencies/turtle_bot_description\n'}
[0.309049] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/ament_index/resource_index/parent_prefix_path/turtle_bot_description\n'}
[0.309086] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.sh\n'}
[0.309122] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.dsv\n'}
[0.309158] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/environment/path.sh\n'}
[0.309192] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/environment/path.dsv\n'}
[0.309228] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/local_setup.bash\n'}
[0.309263] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/local_setup.sh\n'}
[0.309392] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/local_setup.zsh\n'}
[0.309434] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/local_setup.dsv\n'}
[0.309470] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.dsv\n'}
[0.309505] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/ament_index/resource_index/packages/turtle_bot_description\n'}
[0.309541] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig.cmake\n'}
[0.309602] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig-version.cmake\n'}
[0.310372] (turtle_bot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.xml\n'}
[0.312196] (turtle_bot_description) CommandEnded: {'returncode': 0}
[0.337378] (turtle_bot_description) JobEnded: {'identifier': 'turtle_bot_description', 'rc': 0}
[0.338691] (turtle_bot_gazebo) JobStarted: {'identifier': 'turtle_bot_gazebo'}
[0.348999] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'cmake'}
[0.351875] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'build'}
[0.351975] (turtle_bot_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle2/build/turtle_bot_gazebo', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_gazebo'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.407725] (-) TimerEvent: {}
[0.412876] (turtle_bot_gazebo) CommandEnded: {'returncode': 0}
[0.413376] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'install'}
[0.413408] (turtle_bot_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle2/build/turtle_bot_gazebo'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_gazebo'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.420637] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.421757] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds\n'}
[0.422051] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds/turtle_world.world\n'}
[0.422096] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch\n'}
[0.422133] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch/gazebo.launch.py\n'}
[0.422170] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/config\n'}
[0.422205] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/ament_index/resource_index/package_run_dependencies/turtle_bot_gazebo\n'}
[0.422242] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/ament_index/resource_index/parent_prefix_path/turtle_bot_gazebo\n'}
[0.422278] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.sh\n'}
[0.422315] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.dsv\n'}
[0.422351] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.sh\n'}
[0.422396] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.dsv\n'}
[0.422431] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.bash\n'}
[0.422467] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.sh\n'}
[0.422503] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.zsh\n'}
[0.422538] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.dsv\n'}
[0.422707] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv\n'}
[0.422746] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/ament_index/resource_index/packages/turtle_bot_gazebo\n'}
[0.422834] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig.cmake\n'}
[0.425654] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig-version.cmake\n'}
[0.425703] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.xml\n'}
[0.432561] (turtle_bot_gazebo) CommandEnded: {'returncode': 0}
[0.458130] (turtle_bot_gazebo) JobEnded: {'identifier': 'turtle_bot_gazebo', 'rc': 0}
[0.462498] (turtle_bot_navigation) JobStarted: {'identifier': 'turtle_bot_navigation'}
[0.468537] (turtle_bot_slam) JobStarted: {'identifier': 'turtle_bot_slam'}
[0.480510] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'cmake'}
[0.489371] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'build'}
[0.489482] (turtle_bot_navigation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle2/build/turtle_bot_navigation', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_navigation'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.501193] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'cmake'}
[0.508125] (-) TimerEvent: {}
[0.508860] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'build'}
[0.508937] (turtle_bot_slam) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle2/build/turtle_bot_slam', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_slam', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_slam'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.572616] (turtle_bot_slam) CommandEnded: {'returncode': 0}
[0.572995] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'install'}
[0.573015] (turtle_bot_slam) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle2/build/turtle_bot_slam'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_slam', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_slam'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.584878] (turtle_bot_navigation) CommandEnded: {'returncode': 0}
[0.586882] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'install'}
[0.588201] (turtle_bot_navigation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle2/build/turtle_bot_navigation'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_navigation'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.591165] (turtle_bot_slam) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.591260] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/config\n'}
[0.591300] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/config/slam_params.yaml\n'}
[0.591338] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/launch\n'}
[0.591373] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/launch/slam.launch.py\n'}
[0.591409] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/ament_index/resource_index/package_run_dependencies/turtle_bot_slam\n'}
[0.591489] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/ament_index/resource_index/parent_prefix_path/turtle_bot_slam\n'}
[0.591547] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.sh\n'}
[0.591600] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.dsv\n'}
[0.591677] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.sh\n'}
[0.591756] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.dsv\n'}
[0.591836] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.bash\n'}
[0.591918] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.sh\n'}
[0.591998] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.zsh\n'}
[0.592087] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.dsv\n'}
[0.592169] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv\n'}
[0.592229] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/ament_index/resource_index/packages/turtle_bot_slam\n'}
[0.592277] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig.cmake\n'}
[0.592315] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig-version.cmake\n'}
[0.592352] (turtle_bot_slam) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.xml\n'}
[0.594540] (turtle_bot_slam) CommandEnded: {'returncode': 0}
[0.600652] (turtle_bot_navigation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.603008] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/config\n'}
[0.603611] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/config/slam_params.yaml\n'}
[0.604692] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/config/nav2_params_optimized.yaml\n'}
[0.604747] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/config/nav2_params.yaml\n'}
[0.604785] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/launch\n'}
[0.604821] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/launch/navigation.launch.py\n'}
[0.604857] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/maps\n'}
[0.604894] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.yaml\n'}
[0.604931] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.pgm\n'}
[0.604966] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/ament_index/resource_index/package_run_dependencies/turtle_bot_navigation\n'}
[0.605002] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/ament_index/resource_index/parent_prefix_path/turtle_bot_navigation\n'}
[0.605037] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.sh\n'}
[0.605073] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.dsv\n'}
[0.605108] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.sh\n'}
[0.605144] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.dsv\n'}
[0.605180] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.bash\n'}
[0.605224] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.sh\n'}
[0.605259] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.zsh\n'}
[0.605350] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.dsv\n'}
[0.605397] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv\n'}
[0.605435] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/ament_index/resource_index/packages/turtle_bot_navigation\n'}
[0.605479] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig.cmake\n'}
[0.605517] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig-version.cmake\n'}
[0.605553] (turtle_bot_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.xml\n'}
[0.608813] (-) TimerEvent: {}
[0.621934] (turtle_bot_slam) JobEnded: {'identifier': 'turtle_bot_slam', 'rc': 0}
[0.624817] (turtle_bot_navigation) CommandEnded: {'returncode': 0}
[0.655192] (turtle_bot_navigation) JobEnded: {'identifier': 'turtle_bot_navigation', 'rc': 0}
[0.659605] (turtle_bot_bringup) JobStarted: {'identifier': 'turtle_bot_bringup'}
[0.676786] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'cmake'}
[0.685690] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'build'}
[0.685858] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle2/build/turtle_bot_bringup', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_bringup', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.709197] (-) TimerEvent: {}
[0.744250] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[0.750966] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'install'}
[0.751016] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle2/build/turtle_bot_bringup'], 'cwd': '/home/<USER>/turtle2/build/turtle_bot_bringup', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1492'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/turtle2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.E90H82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.139'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle2/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble')]), 'shell': False}
[0.751301] (turtle_bot_uv_system) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/turtle_bot_uv_system', 'build', '--build-base', '/home/<USER>/turtle2/build/turtle_bot_uv_system/build', 'install', '--record', '/home/<USER>/turtle2/build/turtle_bot_uv_system/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/turtle2/src/turtle_bot_uv_system', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_NG:en', 'USER': 'haythem', 'XDG_SESSION_TYPE': 'wayland', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'SYSTEMD_EXEC_PID': '1492', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/turtle2/install', 'ROS_DISTRO': 'humble', 'LOGNAME': 'haythem', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'haythem', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'ROS_LOCALHOST_ONLY': '0', 'PATH': '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/Steri:@/tmp/.ICE-unix/1456,unix/Steri:/tmp/.ICE-unix/1456', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/bc50e339_e518_46d0_b5f8_016205617136', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'RMW_IMPLEMENTATION': 'rmw_cyclonedds_cpp', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.E90H82', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'GNOME_TERMINAL_SERVICE': ':1.139', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/turtle2/install/turtle_bot_uv_system:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/opt/ros/humble', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/turtle2/build/turtle_bot_uv_system', 'TURTLEBOT3_MODEL': 'waffle', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'COLCON': '1', 'VTE_VERSION': '6800', 'CMAKE_PREFIX_PATH': '/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description'}, 'shell': False}
[0.754606] (turtle_bot_bringup) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.755292] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch\n'}
[0.755368] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam.launch.py\n'}
[0.755422] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation_with_uv.launch.py\n'}
[0.755477] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation_optimized.launch.py\n'}
[0.755545] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_simulation.launch.py\n'}
[0.755595] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot.rviz\n'}
[0.755646] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation.launch.py\n'}
[0.755698] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam_navigation.launch.py\n'}
[0.755754] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts\n'}
[0.755810] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/test_navigation.py\n'}
[0.755889] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/launch_rviz.py\n'}
[0.755950] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/wasd_controller.py\n'}
[0.756010] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/test_navigation_enhanced.py\n'}
[0.756359] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/initial_pose_publisher.py\n'}
[0.756672] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/wasd_controller.py\n'}
[0.756739] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/initial_pose_publisher.py\n'}
[0.756801] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/launch_rviz.py\n'}
[0.756872] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/test_navigation.py\n'}
[0.756932] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/test_navigation_enhanced.py\n'}
[0.756995] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/package_run_dependencies/turtle_bot_bringup\n'}
[0.757059] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/parent_prefix_path/turtle_bot_bringup\n'}
[0.757129] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.sh\n'}
[0.757194] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.dsv\n'}
[0.757257] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.sh\n'}
[0.757342] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.dsv\n'}
[0.757407] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.bash\n'}
[0.757469] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.sh\n'}
[0.757529] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.zsh\n'}
[0.757587] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.dsv\n'}
[0.757648] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv\n'}
[0.757709] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/packages/turtle_bot_bringup\n'}
[0.757771] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig.cmake\n'}
[0.757833] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig-version.cmake\n'}
[0.757917] (turtle_bot_bringup) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.xml\n'}
[0.763682] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[0.784034] (turtle_bot_bringup) JobEnded: {'identifier': 'turtle_bot_bringup', 'rc': 0}
[0.940959] (turtle_bot_uv_system) StdoutLine: {'line': b'running egg_info\n'}
[0.941144] (-) TimerEvent: {}
[0.941874] (turtle_bot_uv_system) StdoutLine: {'line': b'writing ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/PKG-INFO\n'}
[0.942062] (turtle_bot_uv_system) StdoutLine: {'line': b'writing dependency_links to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/dependency_links.txt\n'}
[0.942537] (turtle_bot_uv_system) StdoutLine: {'line': b'writing entry points to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/entry_points.txt\n'}
[0.942684] (turtle_bot_uv_system) StdoutLine: {'line': b'writing requirements to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/requires.txt\n'}
[0.943836] (turtle_bot_uv_system) StdoutLine: {'line': b'writing top-level names to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/top_level.txt\n'}
[0.946888] (turtle_bot_uv_system) StdoutLine: {'line': b"reading manifest file '../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/SOURCES.txt'\n"}
[0.947051] (turtle_bot_uv_system) StdoutLine: {'line': b"writing manifest file '../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/SOURCES.txt'\n"}
[0.947656] (turtle_bot_uv_system) StdoutLine: {'line': b'running build\n'}
[0.947763] (turtle_bot_uv_system) StdoutLine: {'line': b'running build_py\n'}
[0.948095] (turtle_bot_uv_system) StdoutLine: {'line': b'running install\n'}
[0.948838] (turtle_bot_uv_system) StdoutLine: {'line': b'running install_lib\n'}
[0.954513] (turtle_bot_uv_system) StdoutLine: {'line': b'running install_data\n'}
[0.954991] (turtle_bot_uv_system) StdoutLine: {'line': b'running install_egg_info\n'}
[0.955731] (turtle_bot_uv_system) StdoutLine: {'line': b"removing '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages/turtle_bot_uv_system-0.0.0-py3.10.egg-info' (and everything under it)\n"}
[0.956122] (turtle_bot_uv_system) StdoutLine: {'line': b'Copying ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages/turtle_bot_uv_system-0.0.0-py3.10.egg-info\n'}
[0.956983] (turtle_bot_uv_system) StdoutLine: {'line': b'running install_scripts\n'}
[0.976784] (turtle_bot_uv_system) StdoutLine: {'line': b'Installing uv_light_controller script to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/turtle_bot_uv_system\n'}
[0.978311] (turtle_bot_uv_system) StdoutLine: {'line': b'Installing uv_map_tracker script to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/turtle_bot_uv_system\n'}
[0.978540] (turtle_bot_uv_system) StdoutLine: {'line': b'Installing uv_visualizer script to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/turtle_bot_uv_system\n'}
[0.979556] (turtle_bot_uv_system) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/turtle2/build/turtle_bot_uv_system/install.log'\n"}
[1.007368] (turtle_bot_uv_system) CommandEnded: {'returncode': 0}
[1.021754] (turtle_bot_uv_system) JobEnded: {'identifier': 'turtle_bot_uv_system', 'rc': 0}
[1.026576] (-) EventReactorShutdown: {}
