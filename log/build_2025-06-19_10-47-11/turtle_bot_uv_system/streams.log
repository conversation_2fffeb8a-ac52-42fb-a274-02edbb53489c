[0.742s] Invoking command in '/home/<USER>/turtle2/src/turtle_bot_uv_system': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
[0.931s] running egg_info
[0.932s] writing ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/PKG-INFO
[0.932s] writing dependency_links to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/dependency_links.txt
[0.933s] writing entry points to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/entry_points.txt
[0.933s] writing requirements to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/requires.txt
[0.934s] writing top-level names to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/top_level.txt
[0.937s] reading manifest file '../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/SOURCES.txt'
[0.937s] writing manifest file '../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/SOURCES.txt'
[0.938s] running build
[0.938s] running build_py
[0.938s] running install
[0.939s] running install_lib
[0.945s] running install_data
[0.945s] running install_egg_info
[0.946s] removing '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages/turtle_bot_uv_system-0.0.0-py3.10.egg-info' (and everything under it)
[0.946s] Copying ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages/turtle_bot_uv_system-0.0.0-py3.10.egg-info
[0.947s] running install_scripts
[0.967s] Installing uv_light_controller script to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/turtle_bot_uv_system
[0.968s] Installing uv_map_tracker script to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/turtle_bot_uv_system
[0.968s] Installing uv_visualizer script to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/turtle_bot_uv_system
[0.970s] writing list of installed files to '/home/<USER>/turtle2/build/turtle_bot_uv_system/install.log'
[0.998s] Invoked command in '/home/<USER>/turtle2/src/turtle_bot_uv_system' returned '0': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
