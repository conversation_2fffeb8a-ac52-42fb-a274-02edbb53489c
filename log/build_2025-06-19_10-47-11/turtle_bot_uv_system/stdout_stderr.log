running egg_info
writing ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/PKG-INFO
writing dependency_links to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/dependency_links.txt
writing entry points to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/entry_points.txt
writing requirements to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/requires.txt
writing top-level names to ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/top_level.txt
reading manifest file '../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/SOURCES.txt'
writing manifest file '../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages/turtle_bot_uv_system-0.0.0-py3.10.egg-info' (and everything under it)
Copying ../../build/turtle_bot_uv_system/turtle_bot_uv_system.egg-info to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages/turtle_bot_uv_system-0.0.0-py3.10.egg-info
running install_scripts
Installing uv_light_controller script to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/turtle_bot_uv_system
Installing uv_map_tracker script to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/turtle_bot_uv_system
Installing uv_visualizer script to /home/<USER>/turtle2/install/turtle_bot_uv_system/lib/turtle_bot_uv_system
writing list of installed files to '/home/<USER>/turtle2/build/turtle_bot_uv_system/install.log'
