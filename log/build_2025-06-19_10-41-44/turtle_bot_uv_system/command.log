Invoking command in '/home/<USER>/turtle2/src/turtle_bot_uv_system': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/turtle2/src/turtle_bot_uv_system' returned '0': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
