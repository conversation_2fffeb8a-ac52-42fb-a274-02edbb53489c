[0.207s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.207s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x71f6970533d0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x71f697052e00>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x71f697052e00>>, mixin_verb=('build',))
[0.434s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.434s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.434s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.434s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.434s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.434s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.434s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/turtle2'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore_ament_install'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_pkg']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_pkg'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_meta']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_meta'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ros']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ros'
[0.460s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_bringup' with type 'ros.ament_cmake' and name 'turtle_bot_bringup'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ignore', 'ignore_ament_install']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore_ament_install'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_pkg']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_pkg'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_meta']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_meta'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ros']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ros'
[0.462s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_description' with type 'ros.ament_cmake' and name 'turtle_bot_description'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ignore', 'ignore_ament_install']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore_ament_install'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_pkg']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_pkg'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_meta']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_meta'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ros']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ros'
[0.463s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_gazebo' with type 'ros.ament_cmake' and name 'turtle_bot_gazebo'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore_ament_install'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_pkg']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_pkg'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_meta']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_meta'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ros']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ros'
[0.464s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_navigation' with type 'ros.ament_cmake' and name 'turtle_bot_navigation'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ignore', 'ignore_ament_install']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore_ament_install'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_pkg']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_pkg'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_meta']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_meta'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ros']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ros'
[0.467s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_slam' with type 'ros.ament_cmake' and name 'turtle_bot_slam'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['ignore', 'ignore_ament_install']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ignore'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ignore_ament_install'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['colcon_pkg']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'colcon_pkg'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['colcon_meta']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'colcon_meta'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['ros']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ros'
[0.468s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_uv_system' with type 'ros.ament_python' and name 'turtle_bot_uv_system'
[0.468s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.469s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.469s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.469s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.469s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.524s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 6 installed packages in /home/<USER>/turtle2/install
[0.525s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 430 installed packages in /opt/ros/humble
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.608s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_args' from command line to 'None'
[0.608s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target' from command line to 'None'
[0.609s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.609s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.609s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.609s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.609s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.609s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.609s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.609s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_description', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_description', 'symlink_install': False, 'test_result_base': None}
[0.610s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_args' from command line to 'None'
[0.610s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_target' from command line to 'None'
[0.610s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.610s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_clean_cache' from command line to 'False'
[0.610s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_clean_first' from command line to 'False'
[0.610s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_force_configure' from command line to 'False'
[0.610s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'ament_cmake_args' from command line to 'None'
[0.610s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'catkin_cmake_args' from command line to 'None'
[0.610s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.610s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_uv_system' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_uv_system', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_uv_system', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_uv_system', 'symlink_install': False, 'test_result_base': None}
[0.611s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_args' from command line to 'None'
[0.611s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target' from command line to 'None'
[0.611s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.611s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[0.611s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[0.611s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[0.611s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[0.612s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[0.612s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.612s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_gazebo', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_gazebo', 'symlink_install': False, 'test_result_base': None}
[0.612s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_args' from command line to 'None'
[0.612s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target' from command line to 'None'
[0.613s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.613s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_cache' from command line to 'False'
[0.613s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_first' from command line to 'False'
[0.613s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_force_configure' from command line to 'False'
[0.613s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'ament_cmake_args' from command line to 'None'
[0.613s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_cmake_args' from command line to 'None'
[0.613s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.613s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_navigation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_navigation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_navigation', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_navigation', 'symlink_install': False, 'test_result_base': None}
[0.613s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_args' from command line to 'None'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target' from command line to 'None'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_cache' from command line to 'False'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_first' from command line to 'False'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_force_configure' from command line to 'False'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'ament_cmake_args' from command line to 'None'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_cmake_args' from command line to 'None'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.614s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_slam' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_slam', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_slam', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_slam', 'symlink_install': False, 'test_result_base': None}
[0.615s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_args' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_first' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_force_configure' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'ament_cmake_args' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.615s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_bringup', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_bringup', 'symlink_install': False, 'test_result_base': None}
[0.615s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.619s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.619s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_description' with build type 'ament_cmake'
[0.619s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_description'
[0.624s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.626s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.626s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.641s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_uv_system' with build type 'ament_python'
[0.641s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_uv_system', 'ament_prefix_path')
[0.641s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/ament_prefix_path.ps1'
[0.642s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/ament_prefix_path.dsv'
[0.643s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/ament_prefix_path.sh'
[0.645s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.646s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.665s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_description -- -j4 -l4
[1.209s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/turtle2/src/turtle_bot_uv_system'
[1.210s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.210s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.230s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_description -- -j4 -l4
[1.264s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_description
[1.297s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[1.298s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_description
[1.306s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description' for CMake module files
[1.313s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description' for CMake config files
[1.314s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[1.314s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[1.316s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[1.317s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[1.319s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/bin'
[1.320s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[1.320s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/lib/python3.10/site-packages'
[1.321s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/bin'
[1.321s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[1.325s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[1.329s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[1.331s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[1.333s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[1.335s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[1.337s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[1.345s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description' for CMake module files
[1.346s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description' for CMake config files
[1.346s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[1.347s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[1.349s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[1.350s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[1.354s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/bin'
[1.354s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[1.355s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/lib/python3.10/site-packages'
[1.355s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_description/bin'
[1.356s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[1.357s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[1.358s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[1.360s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[1.361s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[1.363s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[1.364s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_gazebo' with build type 'ament_cmake'
[1.365s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_gazebo'
[1.365s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.365s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.432s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_gazebo': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_gazebo -- -j4 -l4
[1.528s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_gazebo' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_gazebo -- -j4 -l4
[1.529s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_gazebo': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_gazebo
[1.557s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[1.558s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo' for CMake module files
[1.559s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_gazebo' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_gazebo
[1.566s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo' for CMake config files
[1.566s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[1.567s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[1.568s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[1.572s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[1.577s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/bin'
[1.577s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[1.578s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[1.579s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/bin'
[1.579s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[1.580s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[1.581s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[1.582s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[1.582s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[1.583s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[1.584s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[1.584s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo' for CMake module files
[1.585s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo' for CMake config files
[1.585s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[1.593s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[1.594s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[1.595s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[1.597s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/bin'
[1.597s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[1.597s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[1.597s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_gazebo/bin'
[1.598s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[1.599s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[1.600s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[1.601s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[1.602s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[1.603s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[1.604s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_navigation' with build type 'ament_cmake'
[1.604s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_navigation'
[1.605s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.605s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.621s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_slam' with build type 'ament_cmake'
[1.621s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_slam'
[1.622s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.622s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.669s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_navigation': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_navigation -- -j4 -l4
[1.684s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_slam': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_slam -- -j4 -l4
[1.825s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_slam' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_slam -- -j4 -l4
[1.832s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_slam': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_slam
[1.834s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_navigation' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_navigation -- -j4 -l4
[1.838s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_navigation': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_navigation
[1.862s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[1.863s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam' for CMake module files
[1.867s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam' for CMake config files
[1.875s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[1.877s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[1.879s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[1.880s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[1.872s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_slam' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_slam
[1.882s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/bin'
[1.883s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[1.883s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/lib/python3.10/site-packages'
[1.883s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/bin'
[1.884s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[1.887s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[1.889s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[1.890s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[1.891s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[1.893s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[1.897s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[1.897s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam' for CMake module files
[1.898s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam' for CMake config files
[1.898s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[1.898s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[1.899s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[1.901s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[1.902s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/bin'
[1.903s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[1.903s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/lib/python3.10/site-packages'
[1.903s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_slam/bin'
[1.904s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[1.911s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[1.913s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[1.916s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[1.919s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[1.922s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[1.929s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[1.930s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_navigation' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_navigation
[1.931s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation' for CMake module files
[1.932s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation' for CMake config files
[1.933s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[1.933s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[1.935s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[1.936s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[1.938s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/bin'
[1.938s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[1.938s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/lib/python3.10/site-packages'
[1.939s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/bin'
[1.940s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[1.941s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[1.944s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[1.947s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[1.949s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[1.950s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[1.953s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[1.953s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation' for CMake module files
[1.955s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation' for CMake config files
[1.955s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[1.956s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[1.957s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[1.958s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[1.960s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/bin'
[1.960s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[1.960s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/lib/python3.10/site-packages'
[1.961s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_navigation/bin'
[1.961s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[1.963s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[1.965s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[1.967s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[1.969s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[1.971s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[1.973s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_bringup' with build type 'ament_cmake'
[1.973s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_bringup'
[1.974s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.974s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.027s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_bringup': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_bringup -- -j4 -l4
[2.295s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/src/turtle_bot_uv_system': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
[2.838s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_bringup -- -j4 -l4
[2.846s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_bringup': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_bringup
[2.890s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[2.890s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake module files
[2.891s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake config files
[2.892s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[2.894s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[2.895s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/build/turtle_bot_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_bringup
[2.897s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[2.900s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[2.902s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib'
[2.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[2.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[2.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/python3.10/site-packages'
[2.909s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[2.909s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[2.915s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[2.918s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[2.921s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[2.924s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[2.929s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[2.931s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[2.932s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake module files
[2.932s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup' for CMake config files
[2.933s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[2.934s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[2.937s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[2.938s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[2.941s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib'
[2.941s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[2.941s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[2.941s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/lib/python3.10/site-packages'
[2.942s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_bringup/bin'
[2.943s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[2.947s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[2.949s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[2.953s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[2.957s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[2.960s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[3.095s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system' for CMake module files
[3.097s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system' for CMake config files
[3.098s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib'
[3.098s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/bin'
[3.098s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/pkgconfig/turtle_bot_uv_system.pc'
[3.099s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages'
[3.099s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_uv_system', 'pythonpath')
[3.099s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/pythonpath.ps1'
[3.100s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/pythonpath.dsv'
[3.097s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/src/turtle_bot_uv_system' returned '0': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
[3.105s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/pythonpath.sh'
[3.107s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/bin'
[3.108s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_uv_system)
[3.108s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.ps1'
[3.111s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.dsv'
[3.116s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.sh'
[3.120s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.bash'
[3.121s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.zsh'
[3.122s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_uv_system/share/colcon-core/packages/turtle_bot_uv_system)
[3.126s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[3.127s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[3.127s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[3.128s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[3.145s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[3.146s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[3.146s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[3.175s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[3.176s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.ps1'
[3.179s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle2/install/_local_setup_util_ps1.py'
[3.182s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.ps1'
[3.186s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.sh'
[3.188s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle2/install/_local_setup_util_sh.py'
[3.190s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.sh'
[3.192s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.bash'
[3.194s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.bash'
[3.198s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle2/install/local_setup.zsh'
[3.200s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle2/install/setup.zsh'
