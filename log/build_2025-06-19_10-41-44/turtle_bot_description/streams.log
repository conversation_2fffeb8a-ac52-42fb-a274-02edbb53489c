[0.039s] Invoking command in '/home/<USER>/turtle2/build/turtle_bot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_description -- -j4 -l4
[0.605s] Invoked command in '/home/<USER>/turtle2/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_description -- -j4 -l4
[0.638s] Invoking command in '/home/<USER>/turtle2/build/turtle_bot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_description
[0.655s] -- Install configuration: ""
[0.655s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/urdf
[0.657s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot_gazebo.urdf.xacro
[0.657s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot.urdf.xacro
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/meshes
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/launch
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/launch/display.launch.py
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/launch/robot_state_publisher.launch.py
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/config
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/config/display.rviz
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/ament_index/resource_index/package_run_dependencies/turtle_bot_description
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/ament_index/resource_index/parent_prefix_path/turtle_bot_description
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.sh
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.dsv
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/environment/path.sh
[0.658s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/environment/path.dsv
[0.659s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/local_setup.bash
[0.659s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/local_setup.sh
[0.659s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/local_setup.zsh
[0.659s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/local_setup.dsv
[0.660s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.dsv
[0.660s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/ament_index/resource_index/packages/turtle_bot_description
[0.663s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig.cmake
[0.664s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig-version.cmake
[0.664s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_description/share/turtle_bot_description/package.xml
[0.672s] Invoked command in '/home/<USER>/turtle2/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_description
