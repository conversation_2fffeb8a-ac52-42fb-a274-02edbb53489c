[0.135s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.135s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x73540425afe0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x73540425aaa0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x73540425aaa0>>, mixin_verb=('build',))
[0.341s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.341s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.341s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.341s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.342s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.342s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.342s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/turtle2'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore_ament_install'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_pkg']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_pkg'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_meta']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_meta'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ros']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ros'
[0.358s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_bringup' with type 'ros.ament_cmake' and name 'turtle_bot_bringup'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ignore', 'ignore_ament_install']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore_ament_install'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_pkg']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_pkg'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_meta']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_meta'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ros']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ros'
[0.359s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_description' with type 'ros.ament_cmake' and name 'turtle_bot_description'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ignore', 'ignore_ament_install']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore_ament_install'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_pkg']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_pkg'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_meta']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_meta'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ros']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ros'
[0.359s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_gazebo' with type 'ros.ament_cmake' and name 'turtle_bot_gazebo'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore_ament_install'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_pkg']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_pkg'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_meta']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_meta'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ros']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ros'
[0.360s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_navigation' with type 'ros.ament_cmake' and name 'turtle_bot_navigation'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ignore', 'ignore_ament_install']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore_ament_install'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_pkg']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_pkg'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_meta']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_meta'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ros']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ros'
[0.363s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_slam' with type 'ros.ament_cmake' and name 'turtle_bot_slam'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['ignore', 'ignore_ament_install']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ignore'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ignore_ament_install'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['colcon_pkg']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'colcon_pkg'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['colcon_meta']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'colcon_meta'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extensions ['ros']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_uv_system) by extension 'ros'
[0.365s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_uv_system' with type 'ros.ament_python' and name 'turtle_bot_uv_system'
[0.365s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.404s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.404s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.405s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/turtle2/install/turtle_bot_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.405s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/turtle2/install/turtle_bot_slam' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.405s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/turtle2/install/turtle_bot_navigation' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.405s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/turtle2/install/turtle_bot_gazebo' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.405s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/turtle2/install/turtle_bot_description' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.405s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/turtle2/install/turtle_bot_slam' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.405s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/turtle2/install/turtle_bot_gazebo' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.405s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/turtle2/install/turtle_bot_description' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.406s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 0 installed packages in /home/<USER>/turtle2/install
[0.408s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 430 installed packages in /opt/ros/humble
[0.409s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.463s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_args' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.464s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_description', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_description', 'symlink_install': False, 'test_result_base': None}
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_args' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_target' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_clean_cache' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_clean_first' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'cmake_force_configure' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'ament_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'catkin_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_uv_system' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.465s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_uv_system' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_uv_system', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_uv_system', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_uv_system', 'symlink_install': False, 'test_result_base': None}
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.465s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_gazebo', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_gazebo', 'symlink_install': False, 'test_result_base': None}
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_cache' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_first' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_force_configure' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'ament_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.466s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_navigation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_navigation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_navigation', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_navigation', 'symlink_install': False, 'test_result_base': None}
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_cache' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_first' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_force_configure' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'ament_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.466s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_slam' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_slam', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_slam', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_slam', 'symlink_install': False, 'test_result_base': None}
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_first' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_force_configure' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'ament_cmake_args' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.467s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle2/build/turtle_bot_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle2/install/turtle_bot_bringup', 'merge_install': False, 'path': '/home/<USER>/turtle2/src/turtle_bot_bringup', 'symlink_install': False, 'test_result_base': None}
[0.467s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.468s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.469s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_description' with build type 'ament_cmake'
[0.469s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle2/src/turtle_bot_description'
[0.470s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.471s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.471s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.480s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/turtle2/src/turtle_bot_uv_system' with build type 'ament_python'
[0.481s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_uv_system', 'ament_prefix_path')
[0.482s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/ament_prefix_path.ps1'
[0.482s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/ament_prefix_path.dsv'
[0.483s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/ament_prefix_path.sh'
[0.484s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.484s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.492s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/build/turtle_bot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_navigation:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle2/src/turtle_bot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle2/install/turtle_bot_description
[0.850s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/turtle2/src/turtle_bot_uv_system'
[0.851s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.851s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.689s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle2/src/turtle_bot_uv_system': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
[2.388s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle2/src/turtle_bot_uv_system' returned '0': PYTHONPATH=/home/<USER>/turtle2/build/turtle_bot_uv_system/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/turtle_bot_uv_system build --build-base /home/<USER>/turtle2/build/turtle_bot_uv_system/build install --record /home/<USER>/turtle2/build/turtle_bot_uv_system/install.log --single-version-externally-managed install_data
[2.390s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system' for CMake module files
[2.393s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system' for CMake config files
[2.394s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib'
[2.394s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/bin'
[2.394s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/pkgconfig/turtle_bot_uv_system.pc'
[2.395s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/lib/python3.10/site-packages'
[2.395s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_uv_system', 'pythonpath')
[2.396s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/pythonpath.ps1'
[2.397s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/pythonpath.dsv'
[2.397s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/hook/pythonpath.sh'
[2.398s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle2/install/turtle_bot_uv_system/bin'
[2.398s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_uv_system)
[2.398s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.ps1'
[2.399s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.dsv'
[2.401s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.sh'
[2.404s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.bash'
[2.407s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle2/install/turtle_bot_uv_system/share/turtle_bot_uv_system/package.zsh'
[2.409s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle2/install/turtle_bot_uv_system/share/colcon-core/packages/turtle_bot_uv_system)
