[0.019s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_gazebo
[0.121s] -- The C compiler identification is GNU 11.4.0
[0.193s] -- The CXX compiler identification is GNU 11.4.0
[0.205s] -- Detecting C compiler ABI info
[0.328s] -- Detecting C compiler ABI info - done
[0.343s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.344s] -- Detecting C compile features
[0.345s] -- Detecting C compile features - done
[0.350s] -- Detecting CXX compiler ABI info
[0.491s] -- Detecting CXX compiler ABI info - done
[0.500s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.500s] -- Detecting CXX compile features
[0.501s] -- Detecting CXX compile features - done
[0.505s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.736s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.868s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.971s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.972s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.973s] -- Added test 'lint_cmake' to check CMake code style
[0.973s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.976s] -- Added test 'xmllint' to check XML markup files
[0.976s] -- Configuring done
[0.979s] -- Generating done
[0.981s] -- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_gazebo
[0.991s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_gazebo
[0.995s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[1.081s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[1.085s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
[1.095s] -- Install configuration: ""
[1.095s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds
[1.095s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds/turtle_world.world
[1.095s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch
[1.095s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch/gazebo.launch.py
[1.096s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/config
[1.096s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/package_run_dependencies/turtle_bot_gazebo
[1.096s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/parent_prefix_path/turtle_bot_gazebo
[1.096s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.sh
[1.096s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.dsv
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.sh
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.dsv
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.bash
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.sh
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.zsh
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.dsv
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/packages/turtle_bot_gazebo
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig.cmake
[1.097s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig-version.cmake
[1.098s] -- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.xml
[1.099s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
