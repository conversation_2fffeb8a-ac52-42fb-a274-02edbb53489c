[0.021s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_description
[0.106s] -- The C compiler identification is GNU 11.4.0
[0.200s] -- The CXX compiler identification is GNU 11.4.0
[0.214s] -- Detecting C compiler ABI info
[0.421s] -- Detecting C compiler ABI info - done
[0.441s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.441s] -- Detecting C compile features
[0.443s] -- Detecting C compile features - done
[0.447s] -- Detecting CXX compiler ABI info
[0.655s] -- Detecting CXX compiler ABI info - done
[0.662s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.662s] -- Detecting CXX compile features
[0.663s] -- Detecting CXX compile features - done
[0.665s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.882s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.985s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.072s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.075s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.078s] -- Added test 'lint_cmake' to check CMake code style
[1.078s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.079s] -- Added test 'xmllint' to check XML markup files
[1.079s] -- Configuring done
[1.084s] -- Generating done
[1.088s] -- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_description
[1.094s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_description
[1.095s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
[1.185s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
[1.205s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
[1.223s] -- Install configuration: ""
[1.223s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf
[1.223s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot_gazebo.urdf.xacro
[1.223s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot.urdf.xacro
[1.223s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/meshes
[1.224s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch
[1.224s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/display.launch.py
[1.224s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/robot_state_publisher.launch.py
[1.224s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config
[1.224s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config/display.rviz
[1.224s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/package_run_dependencies/turtle_bot_description
[1.224s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/parent_prefix_path/turtle_bot_description
[1.224s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.sh
[1.225s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.dsv
[1.225s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.sh
[1.225s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.dsv
[1.226s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.bash
[1.226s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.sh
[1.226s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.zsh
[1.226s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.dsv
[1.226s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv
[1.226s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/packages/turtle_bot_description
[1.227s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig.cmake
[1.227s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig-version.cmake
[1.227s] -- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.xml
[1.230s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
