-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_description
-- Install configuration: ""
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot_gazebo.urdf.xacro
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot.urdf.xacro
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/meshes
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/display.launch.py
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/robot_state_publisher.launch.py
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config/display.rviz
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/package_run_dependencies/turtle_bot_description
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/parent_prefix_path/turtle_bot_description
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.sh
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.dsv
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.bash
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.sh
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.zsh
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.dsv
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/packages/turtle_bot_description
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig.cmake
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig-version.cmake
-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.xml
