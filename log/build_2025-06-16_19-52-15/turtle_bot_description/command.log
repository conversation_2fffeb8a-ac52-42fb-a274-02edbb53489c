Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_description
Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_description
Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
