[0.000000] (-) TimerEvent: {}
[0.002742] (turtle_bot_description) JobQueued: {'identifier': 'turtle_bot_description', 'dependencies': OrderedDict()}
[0.002881] (turtle_bot_gazebo) JobQueued: {'identifier': 'turtle_bot_gazebo', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description')])}
[0.002907] (turtle_bot_navigation) JobQueued: {'identifier': 'turtle_bot_navigation', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle/install/turtle_bot_gazebo')])}
[0.002992] (turtle_bot_slam) JobQueued: {'identifier': 'turtle_bot_slam', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle/install/turtle_bot_gazebo')])}
[0.003013] (turtle_bot_bringup) JobQueued: {'identifier': 'turtle_bot_bringup', 'dependencies': OrderedDict([('turtle_bot_description', '/home/<USER>/turtle/install/turtle_bot_description'), ('turtle_bot_gazebo', '/home/<USER>/turtle/install/turtle_bot_gazebo'), ('turtle_bot_navigation', '/home/<USER>/turtle/install/turtle_bot_navigation'), ('turtle_bot_slam', '/home/<USER>/turtle/install/turtle_bot_slam')])}
[0.003356] (turtle_bot_description) JobStarted: {'identifier': 'turtle_bot_description'}
[0.023580] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'cmake'}
[0.023688] (turtle_bot_description) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/turtle/src/turtle_bot_description', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_description'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.101035] (-) TimerEvent: {}
[0.109637] (turtle_bot_description) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.201232] (-) TimerEvent: {}
[0.203470] (turtle_bot_description) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.217499] (turtle_bot_description) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.303057] (-) TimerEvent: {}
[0.403780] (-) TimerEvent: {}
[0.423866] (turtle_bot_description) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.443657] (turtle_bot_description) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.444058] (turtle_bot_description) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.446400] (turtle_bot_description) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.450527] (turtle_bot_description) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.504297] (-) TimerEvent: {}
[0.605175] (-) TimerEvent: {}
[0.658313] (turtle_bot_description) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.665364] (turtle_bot_description) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.665542] (turtle_bot_description) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.666048] (turtle_bot_description) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.668579] (turtle_bot_description) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.708282] (-) TimerEvent: {}
[0.814711] (-) TimerEvent: {}
[0.885582] (turtle_bot_description) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.915342] (-) TimerEvent: {}
[0.988355] (turtle_bot_description) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.021363] (-) TimerEvent: {}
[1.074852] (turtle_bot_description) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[1.077975] (turtle_bot_description) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[1.080953] (turtle_bot_description) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.081200] (turtle_bot_description) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[1.082040] (turtle_bot_description) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.082204] (turtle_bot_description) StdoutLine: {'line': b'-- Configuring done\n'}
[1.087265] (turtle_bot_description) StdoutLine: {'line': b'-- Generating done\n'}
[1.091561] (turtle_bot_description) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_description\n'}
[1.097679] (turtle_bot_description) CommandEnded: {'returncode': 0}
[1.098453] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'build'}
[1.098477] (turtle_bot_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_description', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[1.125232] (-) TimerEvent: {}
[1.188316] (turtle_bot_description) CommandEnded: {'returncode': 0}
[1.190182] (turtle_bot_description) JobProgress: {'identifier': 'turtle_bot_description', 'progress': 'install'}
[1.208022] (turtle_bot_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_description'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[1.225614] (-) TimerEvent: {}
[1.226366] (turtle_bot_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.226661] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf\n'}
[1.226735] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot_gazebo.urdf.xacro\n'}
[1.226798] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/urdf/turtle_bot.urdf.xacro\n'}
[1.226860] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/meshes\n'}
[1.226922] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch\n'}
[1.227362] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/display.launch.py\n'}
[1.227482] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/launch/robot_state_publisher.launch.py\n'}
[1.227560] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config\n'}
[1.227633] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/config/display.rviz\n'}
[1.227707] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/package_run_dependencies/turtle_bot_description\n'}
[1.227782] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/parent_prefix_path/turtle_bot_description\n'}
[1.227857] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.sh\n'}
[1.227957] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/ament_prefix_path.dsv\n'}
[1.228731] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.sh\n'}
[1.228843] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/environment/path.dsv\n'}
[1.229133] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.bash\n'}
[1.229264] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.sh\n'}
[1.229341] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.zsh\n'}
[1.229416] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/local_setup.dsv\n'}
[1.229522] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv\n'}
[1.229633] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/ament_index/resource_index/packages/turtle_bot_description\n'}
[1.230084] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig.cmake\n'}
[1.230156] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/cmake/turtle_bot_descriptionConfig-version.cmake\n'}
[1.230235] (turtle_bot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.xml\n'}
[1.233513] (turtle_bot_description) CommandEnded: {'returncode': 0}
[1.254217] (turtle_bot_description) JobEnded: {'identifier': 'turtle_bot_description', 'rc': 0}
[1.254900] (turtle_bot_gazebo) JobStarted: {'identifier': 'turtle_bot_gazebo'}
[1.270125] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'cmake'}
[1.272276] (turtle_bot_gazebo) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/turtle/src/turtle_bot_gazebo', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_gazebo'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_gazebo'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[1.326198] (-) TimerEvent: {}
[1.375477] (turtle_bot_gazebo) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[1.427868] (-) TimerEvent: {}
[1.448118] (turtle_bot_gazebo) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[1.459832] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[1.528345] (-) TimerEvent: {}
[1.582715] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[1.597563] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[1.599083] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[1.599350] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[1.604431] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[1.628775] (-) TimerEvent: {}
[1.730303] (-) TimerEvent: {}
[1.745369] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[1.754840] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[1.755315] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[1.756135] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[1.759413] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[1.830649] (-) TimerEvent: {}
[1.931517] (-) TimerEvent: {}
[1.990311] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[2.032524] (-) TimerEvent: {}
[2.122515] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[2.135651] (-) TimerEvent: {}
[2.225601] (turtle_bot_gazebo) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[2.227067] (turtle_bot_gazebo) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[2.227717] (turtle_bot_gazebo) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[2.228240] (turtle_bot_gazebo) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[2.230072] (turtle_bot_gazebo) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[2.231179] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Configuring done\n'}
[2.233863] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Generating done\n'}
[2.235956] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_gazebo\n'}
[2.236077] (-) TimerEvent: {}
[2.244713] (turtle_bot_gazebo) CommandEnded: {'returncode': 0}
[2.248860] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'build'}
[2.248953] (turtle_bot_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_gazebo', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_gazebo'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[2.335618] (turtle_bot_gazebo) CommandEnded: {'returncode': 0}
[2.336082] (-) TimerEvent: {}
[2.336990] (turtle_bot_gazebo) JobProgress: {'identifier': 'turtle_bot_gazebo', 'progress': 'install'}
[2.339793] (turtle_bot_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_gazebo'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_gazebo'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[2.349614] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[2.349903] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds\n'}
[2.350156] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/worlds/turtle_world.world\n'}
[2.350266] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch\n'}
[2.350317] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/launch/gazebo.launch.py\n'}
[2.350695] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/config\n'}
[2.350756] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/package_run_dependencies/turtle_bot_gazebo\n'}
[2.350794] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/parent_prefix_path/turtle_bot_gazebo\n'}
[2.350828] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.sh\n'}
[2.351149] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/ament_prefix_path.dsv\n'}
[2.351241] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.sh\n'}
[2.351474] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/environment/path.dsv\n'}
[2.351514] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.bash\n'}
[2.351548] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.sh\n'}
[2.351629] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.zsh\n'}
[2.351781] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/local_setup.dsv\n'}
[2.351838] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv\n'}
[2.352094] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/ament_index/resource_index/packages/turtle_bot_gazebo\n'}
[2.352182] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig.cmake\n'}
[2.352263] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/cmake/turtle_bot_gazeboConfig-version.cmake\n'}
[2.352345] (turtle_bot_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.xml\n'}
[2.353923] (turtle_bot_gazebo) CommandEnded: {'returncode': 0}
[2.377738] (turtle_bot_gazebo) JobEnded: {'identifier': 'turtle_bot_gazebo', 'rc': 0}
[2.378587] (turtle_bot_navigation) JobStarted: {'identifier': 'turtle_bot_navigation'}
[2.385705] (turtle_bot_slam) JobStarted: {'identifier': 'turtle_bot_slam'}
[2.401740] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'cmake'}
[2.401858] (turtle_bot_navigation) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/turtle/src/turtle_bot_navigation', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_navigation'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_navigation'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[2.407819] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'cmake'}
[2.410636] (turtle_bot_slam) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/turtle/src/turtle_bot_slam', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_slam'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_slam', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_slam'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[2.436287] (-) TimerEvent: {}
[2.483225] (turtle_bot_navigation) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[2.494628] (turtle_bot_slam) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[2.536636] (-) TimerEvent: {}
[2.536907] (turtle_bot_navigation) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[2.546293] (turtle_bot_navigation) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[2.559252] (turtle_bot_slam) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[2.571498] (turtle_bot_slam) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[2.648204] (-) TimerEvent: {}
[2.653553] (turtle_bot_navigation) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[2.663435] (turtle_bot_navigation) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[2.663827] (turtle_bot_navigation) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[2.663905] (turtle_bot_navigation) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[2.665322] (turtle_bot_navigation) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[2.668282] (turtle_bot_slam) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[2.688120] (turtle_bot_slam) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[2.688301] (turtle_bot_slam) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[2.688342] (turtle_bot_slam) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[2.689359] (turtle_bot_slam) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[2.749819] (-) TimerEvent: {}
[2.798366] (turtle_bot_navigation) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[2.799257] (turtle_bot_navigation) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[2.799460] (turtle_bot_navigation) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[2.800203] (turtle_bot_navigation) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[2.804452] (turtle_bot_navigation) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[2.814293] (turtle_bot_slam) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[2.823971] (turtle_bot_slam) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[2.824580] (turtle_bot_slam) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[2.825520] (turtle_bot_slam) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[2.829772] (turtle_bot_slam) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[2.862488] (-) TimerEvent: {}
[2.966632] (-) TimerEvent: {}
[2.981849] (turtle_bot_navigation) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[2.989151] (turtle_bot_slam) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[3.069009] (-) TimerEvent: {}
[3.096664] (turtle_bot_navigation) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[3.109722] (turtle_bot_slam) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[3.169742] (-) TimerEvent: {}
[3.188866] (turtle_bot_navigation) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[3.190248] (turtle_bot_navigation) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[3.190737] (turtle_bot_navigation) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[3.190807] (turtle_bot_navigation) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[3.191751] (turtle_bot_navigation) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[3.193135] (turtle_bot_navigation) StdoutLine: {'line': b'-- Configuring done\n'}
[3.195642] (turtle_bot_navigation) StdoutLine: {'line': b'-- Generating done\n'}
[3.199383] (turtle_bot_navigation) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_navigation\n'}
[3.207146] (turtle_bot_navigation) CommandEnded: {'returncode': 0}
[3.207765] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'build'}
[3.207804] (turtle_bot_navigation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_navigation', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_navigation'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[3.213288] (turtle_bot_slam) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[3.213531] (turtle_bot_slam) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[3.214136] (turtle_bot_slam) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[3.214406] (turtle_bot_slam) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[3.215275] (turtle_bot_slam) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[3.216399] (turtle_bot_slam) StdoutLine: {'line': b'-- Configuring done\n'}
[3.221621] (turtle_bot_slam) StdoutLine: {'line': b'-- Generating done\n'}
[3.222283] (turtle_bot_slam) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_slam\n'}
[3.227489] (turtle_bot_slam) CommandEnded: {'returncode': 0}
[3.228073] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'build'}
[3.228263] (turtle_bot_slam) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_slam', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_slam', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_slam'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[3.263831] (turtle_bot_navigation) CommandEnded: {'returncode': 0}
[3.264581] (turtle_bot_navigation) JobProgress: {'identifier': 'turtle_bot_navigation', 'progress': 'install'}
[3.264653] (turtle_bot_navigation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_navigation'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_navigation'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[3.271449] (-) TimerEvent: {}
[3.276410] (turtle_bot_navigation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[3.276577] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/config\n'}
[3.276618] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/config/nav2_params.yaml\n'}
[3.276656] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/launch\n'}
[3.276700] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/launch/navigation.launch.py\n'}
[3.276736] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps\n'}
[3.276772] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.yaml\n'}
[3.276806] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.pgm\n'}
[3.276840] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/package_run_dependencies/turtle_bot_navigation\n'}
[3.276874] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/parent_prefix_path/turtle_bot_navigation\n'}
[3.276910] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.sh\n'}
[3.276944] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.dsv\n'}
[3.276982] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.sh\n'}
[3.277017] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.dsv\n'}
[3.277056] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.bash\n'}
[3.277092] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.sh\n'}
[3.277126] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.zsh\n'}
[3.277924] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.dsv\n'}
[3.278310] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv\n'}
[3.278502] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/packages/turtle_bot_navigation\n'}
[3.278555] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig.cmake\n'}
[3.278644] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig-version.cmake\n'}
[3.278874] (turtle_bot_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.xml\n'}
[3.284424] (turtle_bot_navigation) CommandEnded: {'returncode': 0}
[3.304642] (turtle_bot_navigation) JobEnded: {'identifier': 'turtle_bot_navigation', 'rc': 0}
[3.308224] (turtle_bot_slam) CommandEnded: {'returncode': 0}
[3.309485] (turtle_bot_slam) JobProgress: {'identifier': 'turtle_bot_slam', 'progress': 'install'}
[3.309563] (turtle_bot_slam) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_slam'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_slam', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_slam'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[3.316815] (turtle_bot_slam) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[3.317375] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config\n'}
[3.317456] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config/slam_params.yaml\n'}
[3.317587] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch\n'}
[3.317769] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch/slam.launch.py\n'}
[3.318006] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/package_run_dependencies/turtle_bot_slam\n'}
[3.318229] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/parent_prefix_path/turtle_bot_slam\n'}
[3.318315] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.sh\n'}
[3.318457] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.dsv\n'}
[3.318504] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.sh\n'}
[3.318803] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.dsv\n'}
[3.318849] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.bash\n'}
[3.318884] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.sh\n'}
[3.318918] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.zsh\n'}
[3.319167] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.dsv\n'}
[3.319206] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv\n'}
[3.319345] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/packages/turtle_bot_slam\n'}
[3.319477] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig.cmake\n'}
[3.319518] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig-version.cmake\n'}
[3.319558] (turtle_bot_slam) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.xml\n'}
[3.322360] (turtle_bot_slam) CommandEnded: {'returncode': 0}
[3.342516] (turtle_bot_slam) JobEnded: {'identifier': 'turtle_bot_slam', 'rc': 0}
[3.343626] (turtle_bot_bringup) JobStarted: {'identifier': 'turtle_bot_bringup'}
[3.353342] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'cmake'}
[3.355448] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/turtle/src/turtle_bot_bringup', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_bringup'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_bringup', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[3.371898] (-) TimerEvent: {}
[3.443501] (turtle_bot_bringup) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[3.472197] (-) TimerEvent: {}
[3.525716] (turtle_bot_bringup) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[3.538133] (turtle_bot_bringup) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[3.572399] (-) TimerEvent: {}
[3.673627] (-) TimerEvent: {}
[3.705164] (turtle_bot_bringup) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[3.712745] (turtle_bot_bringup) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[3.713234] (turtle_bot_bringup) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[3.713783] (turtle_bot_bringup) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[3.718428] (turtle_bot_bringup) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[3.774230] (-) TimerEvent: {}
[3.875569] (-) TimerEvent: {}
[3.901218] (turtle_bot_bringup) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[3.922364] (turtle_bot_bringup) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[3.922869] (turtle_bot_bringup) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[3.923609] (turtle_bot_bringup) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[3.928799] (turtle_bot_bringup) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[3.976172] (-) TimerEvent: {}
[4.077246] (-) TimerEvent: {}
[4.177885] (-) TimerEvent: {}
[4.181494] (turtle_bot_bringup) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[4.311972] (-) TimerEvent: {}
[4.317989] (turtle_bot_bringup) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[4.411570] (turtle_bot_bringup) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[4.414399] (-) TimerEvent: {}
[4.415041] (turtle_bot_bringup) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[4.415173] (turtle_bot_bringup) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[4.415228] (turtle_bot_bringup) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[4.415277] (turtle_bot_bringup) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[4.415720] (turtle_bot_bringup) StdoutLine: {'line': b'-- Configuring done\n'}
[4.419454] (turtle_bot_bringup) StdoutLine: {'line': b'-- Generating done\n'}
[4.421130] (turtle_bot_bringup) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_bringup\n'}
[4.429709] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[4.430290] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'build'}
[4.430336] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/turtle/build/turtle_bot_bringup', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_bringup', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[4.497564] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[4.498189] (turtle_bot_bringup) JobProgress: {'identifier': 'turtle_bot_bringup', 'progress': 'install'}
[4.498237] (turtle_bot_bringup) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/turtle/build/turtle_bot_bringup'], 'cwd': '/home/<USER>/turtle/build/turtle_bot_bringup', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en_NG:en'), ('USER', 'haythem'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('SYSTEMD_EXEC_PID', '1481'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haythem'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haythem'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/Steri:@/tmp/.ICE-unix/1449,unix/Steri:/tmp/.ICE-unix/1449'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/0fa42b07_e1be_4305_b7e3_92dedd0c9054'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.39HB82'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.155'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/turtle/build/turtle_bot_bringup'), ('TURTLEBOT3_MODEL', 'waffle'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble')]), 'shell': False}
[4.511432] (turtle_bot_bringup) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[4.511602] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch\n'}
[4.511656] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam.launch.py\n'}
[4.511706] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot.rviz\n'}
[4.511751] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts\n'}
[4.511796] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/wasd_controller.py\n'}
[4.511840] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/lib/turtle_bot_bringup/wasd_controller.py\n'}
[4.511934] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/package_run_dependencies/turtle_bot_bringup\n'}
[4.513758] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/parent_prefix_path/turtle_bot_bringup\n'}
[4.514144] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.sh\n'}
[4.514236] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.dsv\n'}
[4.514316] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.sh\n'}
[4.514382] (-) TimerEvent: {}
[4.514659] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.dsv\n'}
[4.514776] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.bash\n'}
[4.514894] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.sh\n'}
[4.514953] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.zsh\n'}
[4.515632] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.dsv\n'}
[4.515752] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv\n'}
[4.515804] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/packages/turtle_bot_bringup\n'}
[4.515854] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig.cmake\n'}
[4.516098] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig-version.cmake\n'}
[4.516181] (turtle_bot_bringup) StdoutLine: {'line': b'-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.xml\n'}
[4.520068] (turtle_bot_bringup) CommandEnded: {'returncode': 0}
[4.544053] (turtle_bot_bringup) JobEnded: {'identifier': 'turtle_bot_bringup', 'rc': 0}
[4.548677] (-) EventReactorShutdown: {}
