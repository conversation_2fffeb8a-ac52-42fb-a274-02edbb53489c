[0.118s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.118s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7908cc256b30>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7908cc2565f0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7908cc2565f0>>, mixin_verb=('build',))
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.275s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/turtle'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.295s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.295s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.295s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ignore_ament_install'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_pkg']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_pkg'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['colcon_meta']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'colcon_meta'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extensions ['ros']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_bringup) by extension 'ros'
[0.299s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_bringup' with type 'ros.ament_cmake' and name 'turtle_bot_bringup'
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ignore', 'ignore_ament_install']
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore'
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ignore_ament_install'
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_pkg']
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_pkg'
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['colcon_meta']
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'colcon_meta'
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extensions ['ros']
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_description) by extension 'ros'
[0.300s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_description' with type 'ros.ament_cmake' and name 'turtle_bot_description'
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ignore', 'ignore_ament_install']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ignore_ament_install'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_pkg']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_pkg'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['colcon_meta']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'colcon_meta'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extensions ['ros']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_gazebo) by extension 'ros'
[0.301s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_gazebo' with type 'ros.ament_cmake' and name 'turtle_bot_gazebo'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ignore_ament_install'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_pkg']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_pkg'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['colcon_meta']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'colcon_meta'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extensions ['ros']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_navigation) by extension 'ros'
[0.302s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_navigation' with type 'ros.ament_cmake' and name 'turtle_bot_navigation'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ignore', 'ignore_ament_install']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ignore_ament_install'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_pkg']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_pkg'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['colcon_meta']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'colcon_meta'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extensions ['ros']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/turtle_bot_slam) by extension 'ros'
[0.304s] DEBUG:colcon.colcon_core.package_identification:Package 'src/turtle_bot_slam' with type 'ros.ament_cmake' and name 'turtle_bot_slam'
[0.305s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.305s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.305s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.305s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.305s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.341s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.341s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.345s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 430 installed packages in /opt/ros/humble
[0.347s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.385s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_args' from command line to 'None'
[0.385s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target' from command line to 'None'
[0.385s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.385s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.385s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.385s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.385s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.385s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.386s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_description', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_description', 'symlink_install': False, 'test_result_base': None}
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_args' from command line to 'None'
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target' from command line to 'None'
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[0.386s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.386s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_gazebo', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_gazebo', 'symlink_install': False, 'test_result_base': None}
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_args' from command line to 'None'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target' from command line to 'None'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_cache' from command line to 'False'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_clean_first' from command line to 'False'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'cmake_force_configure' from command line to 'False'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'ament_cmake_args' from command line to 'None'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_cmake_args' from command line to 'None'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_navigation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.387s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_navigation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_navigation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_navigation', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_navigation', 'symlink_install': False, 'test_result_base': None}
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_args' from command line to 'None'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target' from command line to 'None'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_cache' from command line to 'False'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_clean_first' from command line to 'False'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'cmake_force_configure' from command line to 'False'
[0.387s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'ament_cmake_args' from command line to 'None'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_cmake_args' from command line to 'None'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_slam' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.388s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_slam' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_slam', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_slam', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_slam', 'symlink_install': False, 'test_result_base': None}
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_args' from command line to 'None'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target' from command line to 'None'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_clean_first' from command line to 'False'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'cmake_force_configure' from command line to 'False'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'ament_cmake_args' from command line to 'None'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'turtle_bot_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.388s] DEBUG:colcon.colcon_core.verb:Building package 'turtle_bot_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/turtle/build/turtle_bot_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/turtle/install/turtle_bot_bringup', 'merge_install': False, 'path': '/home/<USER>/turtle/src/turtle_bot_bringup', 'symlink_install': False, 'test_result_base': None}
[0.388s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.391s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.391s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_description' with build type 'ament_cmake'
[0.391s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_description'
[0.394s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.395s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.395s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.414s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_description
[1.487s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_description
[1.488s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
[1.578s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_description -- -j4 -l4
[1.598s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
[1.622s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[1.624s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_description
[1.626s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake module files
[1.627s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake config files
[1.627s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[1.627s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[1.628s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[1.629s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[1.630s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[1.630s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[1.630s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/python3.10/site-packages'
[1.630s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[1.631s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[1.631s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[1.632s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[1.633s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[1.633s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[1.634s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[1.635s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_description)
[1.635s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake module files
[1.635s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description' for CMake config files
[1.635s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_description', 'cmake_prefix_path')
[1.636s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.ps1'
[1.636s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.dsv'
[1.637s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/hook/cmake_prefix_path.sh'
[1.637s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[1.638s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/pkgconfig/turtle_bot_description.pc'
[1.638s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/lib/python3.10/site-packages'
[1.638s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_description/bin'
[1.639s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.ps1'
[1.639s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.dsv'
[1.640s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.sh'
[1.641s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.bash'
[1.641s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_description/share/turtle_bot_description/package.zsh'
[1.642s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_description/share/colcon-core/packages/turtle_bot_description)
[1.643s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_gazebo' with build type 'ament_cmake'
[1.643s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_gazebo'
[1.643s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.643s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.663s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_gazebo
[2.635s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_gazebo
[2.639s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[2.725s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_gazebo -- -j4 -l4
[2.729s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_gazebo': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
[2.743s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[2.744s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_gazebo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_gazebo
[2.745s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake module files
[2.746s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake config files
[2.746s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[2.746s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[2.747s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[2.747s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[2.749s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[2.749s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[2.750s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[2.750s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[2.751s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[2.751s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[2.752s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[2.753s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[2.753s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[2.753s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[2.754s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_gazebo)
[2.754s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake module files
[2.755s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo' for CMake config files
[2.755s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_gazebo', 'cmake_prefix_path')
[2.755s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.ps1'
[2.756s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.dsv'
[2.757s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/hook/cmake_prefix_path.sh'
[2.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[2.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/pkgconfig/turtle_bot_gazebo.pc'
[2.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/lib/python3.10/site-packages'
[2.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_gazebo/bin'
[2.759s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.ps1'
[2.760s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.dsv'
[2.761s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.sh'
[2.762s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.bash'
[2.763s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_gazebo/share/turtle_bot_gazebo/package.zsh'
[2.764s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_gazebo/share/colcon-core/packages/turtle_bot_gazebo)
[2.765s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_navigation' with build type 'ament_cmake'
[2.766s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_navigation'
[2.766s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.766s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.773s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_slam' with build type 'ament_cmake'
[2.774s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_slam'
[2.774s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.774s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.796s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_navigation -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_navigation
[2.804s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_slam -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_slam
[3.597s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_navigation -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_navigation
[3.597s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_navigation -- -j4 -l4
[3.617s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_slam -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_slam
[3.618s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[3.654s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_navigation -- -j4 -l4
[3.654s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_navigation
[3.672s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[3.673s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake module files
[3.675s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_navigation
[3.676s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake config files
[3.676s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[3.677s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[3.678s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[3.678s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[3.679s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[3.679s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[3.680s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/python3.10/site-packages'
[3.680s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[3.680s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[3.681s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[3.681s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[3.682s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[3.682s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[3.682s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[3.683s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_navigation)
[3.683s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake module files
[3.685s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation' for CMake config files
[3.685s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_navigation', 'cmake_prefix_path')
[3.685s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.ps1'
[3.687s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.dsv'
[3.687s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/hook/cmake_prefix_path.sh'
[3.689s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[3.689s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/pkgconfig/turtle_bot_navigation.pc'
[3.689s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/lib/python3.10/site-packages'
[3.689s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_navigation/bin'
[3.689s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.ps1'
[3.690s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv'
[3.691s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.sh'
[3.691s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.bash'
[3.692s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.zsh'
[3.693s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_navigation/share/colcon-core/packages/turtle_bot_navigation)
[3.698s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[3.699s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
[3.711s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[3.712s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
[3.713s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake module files
[3.713s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake config files
[3.713s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[3.714s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[3.714s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[3.715s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[3.715s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[3.715s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[3.715s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/python3.10/site-packages'
[3.715s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[3.716s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[3.716s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[3.717s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[3.717s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[3.718s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[3.718s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[3.718s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_slam)
[3.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake module files
[3.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam' for CMake config files
[3.719s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_slam', 'cmake_prefix_path')
[3.719s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.ps1'
[3.720s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.dsv'
[3.721s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/hook/cmake_prefix_path.sh'
[3.722s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[3.722s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/pkgconfig/turtle_bot_slam.pc'
[3.723s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/lib/python3.10/site-packages'
[3.723s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_slam/bin'
[3.723s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.ps1'
[3.724s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv'
[3.725s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.sh'
[3.726s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.bash'
[3.726s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.zsh'
[3.727s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_slam/share/colcon-core/packages/turtle_bot_slam)
[3.728s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/turtle/src/turtle_bot_bringup' with build type 'ament_cmake'
[3.728s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/turtle/src/turtle_bot_bringup'
[3.728s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.728s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.747s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_bringup -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_bringup
[4.819s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_bringup -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_bringup
[4.820s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_bringup -- -j4 -l4
[4.887s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_bringup -- -j4 -l4
[4.888s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_bringup
[4.907s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[4.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake module files
[4.909s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake config files
[4.909s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[4.909s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[4.910s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_bringup
[4.910s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[4.911s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[4.911s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib'
[4.911s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[4.912s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[4.912s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/python3.10/site-packages'
[4.912s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[4.913s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[4.915s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[4.915s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[4.917s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[4.920s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[4.921s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[4.922s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(turtle_bot_bringup)
[4.922s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake module files
[4.922s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup' for CMake config files
[4.923s] Level 1:colcon.colcon_core.shell:create_environment_hook('turtle_bot_bringup', 'cmake_prefix_path')
[4.923s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.ps1'
[4.924s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.dsv'
[4.925s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/hook/cmake_prefix_path.sh'
[4.926s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib'
[4.926s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[4.926s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/pkgconfig/turtle_bot_bringup.pc'
[4.927s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/lib/python3.10/site-packages'
[4.927s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/turtle/install/turtle_bot_bringup/bin'
[4.927s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.ps1'
[4.928s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv'
[4.929s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.sh'
[4.930s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.bash'
[4.931s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.zsh'
[4.932s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/turtle/install/turtle_bot_bringup/share/colcon-core/packages/turtle_bot_bringup)
[4.935s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[4.935s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[4.936s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[4.936s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[4.946s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[4.946s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[4.946s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[4.962s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[4.963s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.ps1'
[4.976s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle/install/_local_setup_util_ps1.py'
[4.978s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.ps1'
[4.980s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.sh'
[4.980s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/turtle/install/_local_setup_util_sh.py'
[4.981s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.sh'
[4.983s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.bash'
[4.984s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.bash'
[4.985s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/turtle/install/local_setup.zsh'
[4.986s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/turtle/install/setup.zsh'
