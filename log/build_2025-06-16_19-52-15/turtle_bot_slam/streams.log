[0.029s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_slam -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_slam
[0.109s] -- The C compiler identification is GNU 11.4.0
[0.174s] -- The CXX compiler identification is GNU 11.4.0
[0.186s] -- Detecting C compiler ABI info
[0.283s] -- Detecting C compiler ABI info - done
[0.303s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.303s] -- Detecting C compile features
[0.303s] -- Detecting C compile features - done
[0.304s] -- Detecting CXX compiler AB<PERSON> info
[0.429s] -- Detecting CXX compiler ABI info - done
[0.438s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.439s] -- Detecting CXX compile features
[0.440s] -- Detecting CXX compile features - done
[0.444s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.604s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.724s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.828s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.828s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.829s] -- Added test 'lint_cmake' to check CMake code style
[0.829s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.830s] -- Added test 'xmllint' to check XML markup files
[0.831s] -- Configuring done
[0.836s] -- Generating done
[0.837s] -- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_slam
[0.842s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_slam -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_slam
[0.843s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[0.923s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_slam -- -j4 -l4
[0.924s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_slam': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
[0.932s] -- Install configuration: ""
[0.932s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config
[0.932s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/config/slam_params.yaml
[0.932s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch
[0.932s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/launch/slam.launch.py
[0.932s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/package_run_dependencies/turtle_bot_slam
[0.933s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/parent_prefix_path/turtle_bot_slam
[0.933s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.sh
[0.933s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/ament_prefix_path.dsv
[0.933s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.sh
[0.933s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/environment/path.dsv
[0.933s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.bash
[0.933s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.sh
[0.933s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.zsh
[0.933s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/local_setup.dsv
[0.934s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.dsv
[0.934s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/ament_index/resource_index/packages/turtle_bot_slam
[0.934s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig.cmake
[0.934s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/cmake/turtle_bot_slamConfig-version.cmake
[0.934s] -- Installing: /home/<USER>/turtle/install/turtle_bot_slam/share/turtle_bot_slam/package.xml
[0.937s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_slam' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_slam
