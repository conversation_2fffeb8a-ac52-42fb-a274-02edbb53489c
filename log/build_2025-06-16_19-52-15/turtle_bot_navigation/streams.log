[0.028s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_navigation -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_navigation
[0.105s] -- The C compiler identification is GNU 11.4.0
[0.158s] -- The CXX compiler identification is GNU 11.4.0
[0.168s] -- Detecting C compiler ABI info
[0.275s] -- Detecting C compiler ABI info - done
[0.285s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.285s] -- Detecting C compile features
[0.285s] -- Detecting C compile features - done
[0.287s] -- Detecting CXX compiler AB<PERSON> info
[0.420s] -- Detecting CXX compiler AB<PERSON> info - done
[0.421s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.421s] -- Detecting CXX compile features
[0.422s] -- Detecting CXX compile features - done
[0.426s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.603s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.718s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.810s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.812s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.812s] -- Added test 'lint_cmake' to check CMake code style
[0.812s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.813s] -- Added test 'xmllint' to check XML markup files
[0.815s] -- Configuring done
[0.817s] -- Generating done
[0.821s] -- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_navigation
[0.829s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_navigation -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_navigation
[0.829s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_navigation -- -j4 -l4
[0.886s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_navigation -- -j4 -l4
[0.886s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_navigation': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_navigation
[0.898s] -- Install configuration: ""
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/config
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/config/nav2_params.yaml
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/launch
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/launch/navigation.launch.py
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.yaml
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/maps/map.pgm
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/package_run_dependencies/turtle_bot_navigation
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/parent_prefix_path/turtle_bot_navigation
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.sh
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/ament_prefix_path.dsv
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.sh
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/environment/path.dsv
[0.898s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.bash
[0.899s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.sh
[0.899s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.zsh
[0.900s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/local_setup.dsv
[0.900s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.dsv
[0.900s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/ament_index/resource_index/packages/turtle_bot_navigation
[0.900s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig.cmake
[0.900s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/cmake/turtle_bot_navigationConfig-version.cmake
[0.900s] -- Installing: /home/<USER>/turtle/install/turtle_bot_navigation/share/turtle_bot_navigation/package.xml
[0.907s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_navigation' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_navigation
