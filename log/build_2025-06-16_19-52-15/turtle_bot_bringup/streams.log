[0.014s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_bringup -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_bringup
[0.100s] -- The C compiler identification is GNU 11.4.0
[0.182s] -- The CXX compiler identification is GNU 11.4.0
[0.195s] -- Detecting C compiler ABI info
[0.362s] -- Detecting C compiler ABI info - done
[0.369s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.370s] -- Detecting C compile features
[0.370s] -- Detecting C compile features - done
[0.375s] -- Detecting CXX compiler ABI info
[0.558s] -- Detecting CXX compiler ABI info - done
[0.579s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.579s] -- Detecting CXX compile features
[0.580s] -- Detecting CXX compile features - done
[0.585s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.838s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.975s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.068s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.071s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.072s] -- Added test 'lint_cmake' to check CMake code style
[1.072s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.072s] -- Added test 'xmllint' to check XML markup files
[1.072s] -- Configuring done
[1.077s] -- Generating done
[1.078s] -- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_bringup
[1.086s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake /home/<USER>/turtle/src/turtle_bot_bringup -DCMAKE_INSTALL_PREFIX=/home/<USER>/turtle/install/turtle_bot_bringup
[1.087s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_bringup -- -j4 -l4
[1.154s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle/build/turtle_bot_bringup -- -j4 -l4
[1.155s] Invoking command in '/home/<USER>/turtle/build/turtle_bot_bringup': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_bringup
[1.168s] -- Install configuration: ""
[1.168s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch
[1.168s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam.launch.py
[1.168s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot.rviz
[1.168s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts
[1.168s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/wasd_controller.py
[1.168s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/lib/turtle_bot_bringup/wasd_controller.py
[1.168s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/package_run_dependencies/turtle_bot_bringup
[1.170s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/parent_prefix_path/turtle_bot_bringup
[1.171s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.sh
[1.171s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.dsv
[1.171s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.sh
[1.171s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.dsv
[1.171s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.bash
[1.171s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.sh
[1.172s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.zsh
[1.172s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.dsv
[1.172s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv
[1.172s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/packages/turtle_bot_bringup
[1.172s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig.cmake
[1.173s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig-version.cmake
[1.173s] -- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.xml
[1.177s] Invoked command in '/home/<USER>/turtle/build/turtle_bot_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/turtle/install/turtle_bot_slam:/home/<USER>/turtle/install/turtle_bot_navigation:/home/<USER>/turtle/install/turtle_bot_gazebo:/home/<USER>/turtle/install/turtle_bot_description:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle/build/turtle_bot_bringup
