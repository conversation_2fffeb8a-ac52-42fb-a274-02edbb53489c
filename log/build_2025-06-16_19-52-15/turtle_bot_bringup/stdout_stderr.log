-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/turtle/build/turtle_bot_bringup
-- Install configuration: ""
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam.launch.py
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot.rviz
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/wasd_controller.py
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/lib/turtle_bot_bringup/wasd_controller.py
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/package_run_dependencies/turtle_bot_bringup
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/parent_prefix_path/turtle_bot_bringup
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.sh
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.dsv
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.bash
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.sh
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.zsh
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.dsv
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/ament_index/resource_index/packages/turtle_bot_bringup
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig.cmake
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig-version.cmake
-- Installing: /home/<USER>/turtle/install/turtle_bot_bringup/share/turtle_bot_bringup/package.xml
