[0.063s] Invoking command in '/home/<USER>/turtle2/build/turtle_bot_bringup': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_bringup -- -j4 -l4
[0.099s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.334s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.451s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.457s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.458s] -- Added test 'lint_cmake' to check CMake code style
[0.458s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.458s] -- Added test 'xmllint' to check XML markup files
[0.458s] -- Configuring done
[0.468s] -- Generating done
[0.480s] -- Build files have been written to: /home/<USER>/turtle2/build/turtle_bot_bringup
[0.534s] Invoked command in '/home/<USER>/turtle2/build/turtle_bot_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/turtle2/build/turtle_bot_bringup -- -j4 -l4
[0.571s] Invoking command in '/home/<USER>/turtle2/build/turtle_bot_bringup': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_bringup
[0.598s] -- Install configuration: ""
[0.598s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch
[0.598s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam.launch.py
[0.598s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation_with_uv.launch.py
[0.598s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_simulation.launch.py
[0.598s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot.rviz
[0.599s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation.launch.py
[0.599s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts
[0.599s] -- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/test_navigation.py
[0.599s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/launch_rviz.py
[0.599s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/wasd_controller.py
[0.599s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/initial_pose_publisher.py
[0.599s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/wasd_controller.py
[0.599s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/initial_pose_publisher.py
[0.599s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/launch_rviz.py
[0.600s] -- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/test_navigation.py
[0.600s] -- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/package_run_dependencies/turtle_bot_bringup
[0.600s] -- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/parent_prefix_path/turtle_bot_bringup
[0.600s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.sh
[0.600s] -- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.dsv
[0.600s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.sh
[0.600s] -- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.dsv
[0.600s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.bash
[0.600s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.sh
[0.600s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.zsh
[0.600s] -- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.dsv
[0.600s] -- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv
[0.600s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/packages/turtle_bot_bringup
[0.600s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig.cmake
[0.600s] -- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig-version.cmake
[0.600s] -- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.xml
[0.610s] Invoked command in '/home/<USER>/turtle2/build/turtle_bot_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/turtle2/install/turtle_bot_slam:/home/<USER>/turtle2/install/turtle_bot_navigation:/home/<USER>/turtle2/install/turtle_bot_gazebo:/home/<USER>/turtle2/install/turtle_bot_description:/home/<USER>/turtle2/install/turtle_bot_bringup:/home/<USER>/turtle2/install/turtle_bot_uv_system:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/turtle2/build/turtle_bot_bringup
