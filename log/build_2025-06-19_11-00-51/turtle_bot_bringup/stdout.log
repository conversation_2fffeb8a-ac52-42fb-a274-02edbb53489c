-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/turtle2/build/turtle_bot_bringup
-- Install configuration: ""
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam.launch.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation_with_uv.launch.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation_optimized.launch.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_simulation.launch.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot.rviz
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam_navigation_fixed.launch.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_navigation.launch.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/launch/turtle_bot_slam_navigation.launch.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/test_navigation.py
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/navigation_diagnostics.py
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/slam_initialization.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/launch_rviz.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/wasd_controller.py
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/test_rviz_goals.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/test_navigation_enhanced.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/scripts/initial_pose_publisher.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/wasd_controller.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/initial_pose_publisher.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/launch_rviz.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/test_navigation.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/test_navigation_enhanced.py
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/slam_initialization.py
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/navigation_diagnostics.py
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/lib/turtle_bot_bringup/test_rviz_goals.py
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/package_run_dependencies/turtle_bot_bringup
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/parent_prefix_path/turtle_bot_bringup
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.sh
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/environment/path.dsv
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.bash
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.sh
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.zsh
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/local_setup.dsv
-- Installing: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.dsv
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/ament_index/resource_index/packages/turtle_bot_bringup
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig.cmake
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/cmake/turtle_bot_bringupConfig-version.cmake
-- Up-to-date: /home/<USER>/turtle2/install/turtle_bot_bringup/share/turtle_bot_bringup/package.xml
